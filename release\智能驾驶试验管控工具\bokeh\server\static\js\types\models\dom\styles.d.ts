import { Model } from "../../model";
import * as p from "../../core/properties";
export declare namespace Styles {
    type Attrs = p.AttrsOf<Props>;
    type Props = Model.Props & {
        align_content: p.Property<string | null>;
        align_items: p.Property<string | null>;
        align_self: p.Property<string | null>;
        alignment_baseline: p.Property<string | null>;
        all: p.Property<string | null>;
        animation: p.Property<string | null>;
        animation_delay: p.Property<string | null>;
        animation_direction: p.Property<string | null>;
        animation_duration: p.Property<string | null>;
        animation_fill_mode: p.Property<string | null>;
        animation_iteration_count: p.Property<string | null>;
        animation_name: p.Property<string | null>;
        animation_play_state: p.Property<string | null>;
        animation_timing_function: p.Property<string | null>;
        backface_visibility: p.Property<string | null>;
        background: p.Property<string | null>;
        background_attachment: p.Property<string | null>;
        background_clip: p.Property<string | null>;
        background_color: p.Property<string | null>;
        background_image: p.Property<string | null>;
        background_origin: p.Property<string | null>;
        background_position: p.Property<string | null>;
        background_position_x: p.Property<string | null>;
        background_position_y: p.Property<string | null>;
        background_repeat: p.Property<string | null>;
        background_size: p.Property<string | null>;
        baseline_shift: p.Property<string | null>;
        block_size: p.Property<string | null>;
        border: p.Property<string | null>;
        border_block_end: p.Property<string | null>;
        border_block_end_color: p.Property<string | null>;
        border_block_end_style: p.Property<string | null>;
        border_block_end_width: p.Property<string | null>;
        border_block_start: p.Property<string | null>;
        border_block_start_color: p.Property<string | null>;
        border_block_start_style: p.Property<string | null>;
        border_block_start_width: p.Property<string | null>;
        border_bottom: p.Property<string | null>;
        border_bottom_color: p.Property<string | null>;
        border_bottom_left_radius: p.Property<string | null>;
        border_bottom_right_radius: p.Property<string | null>;
        border_bottom_style: p.Property<string | null>;
        border_bottom_width: p.Property<string | null>;
        border_collapse: p.Property<string | null>;
        border_color: p.Property<string | null>;
        border_image: p.Property<string | null>;
        border_image_outset: p.Property<string | null>;
        border_image_repeat: p.Property<string | null>;
        border_image_slice: p.Property<string | null>;
        border_image_source: p.Property<string | null>;
        border_image_width: p.Property<string | null>;
        border_inline_end: p.Property<string | null>;
        border_inline_end_color: p.Property<string | null>;
        border_inline_end_style: p.Property<string | null>;
        border_inline_end_width: p.Property<string | null>;
        border_inline_start: p.Property<string | null>;
        border_inline_start_color: p.Property<string | null>;
        border_inline_start_style: p.Property<string | null>;
        border_inline_start_width: p.Property<string | null>;
        border_left: p.Property<string | null>;
        border_left_color: p.Property<string | null>;
        border_left_style: p.Property<string | null>;
        border_left_width: p.Property<string | null>;
        border_radius: p.Property<string | null>;
        border_right: p.Property<string | null>;
        border_right_color: p.Property<string | null>;
        border_right_style: p.Property<string | null>;
        border_right_width: p.Property<string | null>;
        border_spacing: p.Property<string | null>;
        border_style: p.Property<string | null>;
        border_top: p.Property<string | null>;
        border_top_color: p.Property<string | null>;
        border_top_left_radius: p.Property<string | null>;
        border_top_right_radius: p.Property<string | null>;
        border_top_style: p.Property<string | null>;
        border_top_width: p.Property<string | null>;
        border_width: p.Property<string | null>;
        bottom: p.Property<string | null>;
        box_shadow: p.Property<string | null>;
        box_sizing: p.Property<string | null>;
        break_after: p.Property<string | null>;
        break_before: p.Property<string | null>;
        break_inside: p.Property<string | null>;
        caption_side: p.Property<string | null>;
        caret_color: p.Property<string | null>;
        clear: p.Property<string | null>;
        clip: p.Property<string | null>;
        clip_path: p.Property<string | null>;
        clip_rule: p.Property<string | null>;
        color: p.Property<string | null>;
        color_interpolation: p.Property<string | null>;
        color_interpolation_filters: p.Property<string | null>;
        column_count: p.Property<string | null>;
        column_fill: p.Property<string | null>;
        column_gap: p.Property<string | null>;
        column_rule: p.Property<string | null>;
        column_rule_color: p.Property<string | null>;
        column_rule_style: p.Property<string | null>;
        column_rule_width: p.Property<string | null>;
        column_span: p.Property<string | null>;
        column_width: p.Property<string | null>;
        columns: p.Property<string | null>;
        content: p.Property<string | null>;
        counter_increment: p.Property<string | null>;
        counter_reset: p.Property<string | null>;
        css_float: p.Property<string | null>;
        css_text: p.Property<string | null>;
        cursor: p.Property<string | null>;
        direction: p.Property<string | null>;
        display: p.Property<string | null>;
        dominant_baseline: p.Property<string | null>;
        empty_cells: p.Property<string | null>;
        fill: p.Property<string | null>;
        fill_opacity: p.Property<string | null>;
        fill_rule: p.Property<string | null>;
        filter: p.Property<string | null>;
        flex: p.Property<string | null>;
        flex_basis: p.Property<string | null>;
        flex_direction: p.Property<string | null>;
        flex_flow: p.Property<string | null>;
        flex_grow: p.Property<string | null>;
        flex_shrink: p.Property<string | null>;
        flex_wrap: p.Property<string | null>;
        float: p.Property<string | null>;
        flood_color: p.Property<string | null>;
        flood_opacity: p.Property<string | null>;
        font: p.Property<string | null>;
        font_family: p.Property<string | null>;
        font_feature_settings: p.Property<string | null>;
        font_kerning: p.Property<string | null>;
        font_size: p.Property<string | null>;
        font_size_adjust: p.Property<string | null>;
        font_stretch: p.Property<string | null>;
        font_style: p.Property<string | null>;
        font_synthesis: p.Property<string | null>;
        font_variant: p.Property<string | null>;
        font_variant_caps: p.Property<string | null>;
        font_variant_east_asian: p.Property<string | null>;
        font_variant_ligatures: p.Property<string | null>;
        font_variant_numeric: p.Property<string | null>;
        font_variant_position: p.Property<string | null>;
        font_weight: p.Property<string | null>;
        gap: p.Property<string | null>;
        glyph_orientation_vertical: p.Property<string | null>;
        grid: p.Property<string | null>;
        grid_area: p.Property<string | null>;
        grid_auto_columns: p.Property<string | null>;
        grid_auto_flow: p.Property<string | null>;
        grid_auto_rows: p.Property<string | null>;
        grid_column: p.Property<string | null>;
        grid_column_end: p.Property<string | null>;
        grid_column_gap: p.Property<string | null>;
        grid_column_start: p.Property<string | null>;
        grid_gap: p.Property<string | null>;
        grid_row: p.Property<string | null>;
        grid_row_end: p.Property<string | null>;
        grid_row_gap: p.Property<string | null>;
        grid_row_start: p.Property<string | null>;
        grid_template: p.Property<string | null>;
        grid_template_areas: p.Property<string | null>;
        grid_template_columns: p.Property<string | null>;
        grid_template_rows: p.Property<string | null>;
        height: p.Property<string | null>;
        hyphens: p.Property<string | null>;
        image_orientation: p.Property<string | null>;
        image_rendering: p.Property<string | null>;
        inline_size: p.Property<string | null>;
        justify_content: p.Property<string | null>;
        justify_items: p.Property<string | null>;
        justify_self: p.Property<string | null>;
        left: p.Property<string | null>;
        letter_spacing: p.Property<string | null>;
        lighting_color: p.Property<string | null>;
        line_break: p.Property<string | null>;
        line_height: p.Property<string | null>;
        list_style: p.Property<string | null>;
        list_style_image: p.Property<string | null>;
        list_style_position: p.Property<string | null>;
        list_style_type: p.Property<string | null>;
        margin: p.Property<string | null>;
        margin_block_end: p.Property<string | null>;
        margin_block_start: p.Property<string | null>;
        margin_bottom: p.Property<string | null>;
        margin_inline_end: p.Property<string | null>;
        margin_inline_start: p.Property<string | null>;
        margin_left: p.Property<string | null>;
        margin_right: p.Property<string | null>;
        margin_top: p.Property<string | null>;
        marker: p.Property<string | null>;
        marker_end: p.Property<string | null>;
        marker_mid: p.Property<string | null>;
        marker_start: p.Property<string | null>;
        mask: p.Property<string | null>;
        mask_composite: p.Property<string | null>;
        mask_image: p.Property<string | null>;
        mask_position: p.Property<string | null>;
        mask_repeat: p.Property<string | null>;
        mask_size: p.Property<string | null>;
        mask_type: p.Property<string | null>;
        max_block_size: p.Property<string | null>;
        max_height: p.Property<string | null>;
        max_inline_size: p.Property<string | null>;
        max_width: p.Property<string | null>;
        min_block_size: p.Property<string | null>;
        min_height: p.Property<string | null>;
        min_inline_size: p.Property<string | null>;
        min_width: p.Property<string | null>;
        object_fit: p.Property<string | null>;
        object_position: p.Property<string | null>;
        opacity: p.Property<string | null>;
        order: p.Property<string | null>;
        orphans: p.Property<string | null>;
        outline: p.Property<string | null>;
        outline_color: p.Property<string | null>;
        outline_offset: p.Property<string | null>;
        outline_style: p.Property<string | null>;
        outline_width: p.Property<string | null>;
        overflow: p.Property<string | null>;
        overflow_anchor: p.Property<string | null>;
        overflow_wrap: p.Property<string | null>;
        overflow_x: p.Property<string | null>;
        overflow_y: p.Property<string | null>;
        overscroll_behavior: p.Property<string | null>;
        overscroll_behavior_block: p.Property<string | null>;
        overscroll_behavior_inline: p.Property<string | null>;
        overscroll_behavior_x: p.Property<string | null>;
        overscroll_behavior_y: p.Property<string | null>;
        padding: p.Property<string | null>;
        padding_block_end: p.Property<string | null>;
        padding_block_start: p.Property<string | null>;
        padding_bottom: p.Property<string | null>;
        padding_inline_end: p.Property<string | null>;
        padding_inline_start: p.Property<string | null>;
        padding_left: p.Property<string | null>;
        padding_right: p.Property<string | null>;
        padding_top: p.Property<string | null>;
        page_break_after: p.Property<string | null>;
        page_break_before: p.Property<string | null>;
        page_break_inside: p.Property<string | null>;
        paint_order: p.Property<string | null>;
        perspective: p.Property<string | null>;
        perspective_origin: p.Property<string | null>;
        place_content: p.Property<string | null>;
        place_items: p.Property<string | null>;
        place_self: p.Property<string | null>;
        pointer_events: p.Property<string | null>;
        position: p.Property<string | null>;
        quotes: p.Property<string | null>;
        resize: p.Property<string | null>;
        right: p.Property<string | null>;
        rotate: p.Property<string | null>;
        row_gap: p.Property<string | null>;
        ruby_align: p.Property<string | null>;
        ruby_position: p.Property<string | null>;
        scale: p.Property<string | null>;
        scroll_behavior: p.Property<string | null>;
        shape_rendering: p.Property<string | null>;
        stop_color: p.Property<string | null>;
        stop_opacity: p.Property<string | null>;
        stroke: p.Property<string | null>;
        stroke_dasharray: p.Property<string | null>;
        stroke_dashoffset: p.Property<string | null>;
        stroke_linecap: p.Property<string | null>;
        stroke_linejoin: p.Property<string | null>;
        stroke_miterlimit: p.Property<string | null>;
        stroke_opacity: p.Property<string | null>;
        stroke_width: p.Property<string | null>;
        tab_size: p.Property<string | null>;
        table_layout: p.Property<string | null>;
        text_align: p.Property<string | null>;
        text_align_last: p.Property<string | null>;
        text_anchor: p.Property<string | null>;
        text_combine_upright: p.Property<string | null>;
        text_decoration: p.Property<string | null>;
        text_decoration_color: p.Property<string | null>;
        text_decoration_line: p.Property<string | null>;
        text_decoration_style: p.Property<string | null>;
        text_emphasis: p.Property<string | null>;
        text_emphasis_color: p.Property<string | null>;
        text_emphasis_position: p.Property<string | null>;
        text_emphasis_style: p.Property<string | null>;
        text_indent: p.Property<string | null>;
        text_justify: p.Property<string | null>;
        text_orientation: p.Property<string | null>;
        text_overflow: p.Property<string | null>;
        text_rendering: p.Property<string | null>;
        text_shadow: p.Property<string | null>;
        text_transform: p.Property<string | null>;
        text_underline_position: p.Property<string | null>;
        top: p.Property<string | null>;
        touch_action: p.Property<string | null>;
        transform: p.Property<string | null>;
        transform_box: p.Property<string | null>;
        transform_origin: p.Property<string | null>;
        transform_style: p.Property<string | null>;
        transition: p.Property<string | null>;
        transition_delay: p.Property<string | null>;
        transition_duration: p.Property<string | null>;
        transition_property: p.Property<string | null>;
        transition_timing_function: p.Property<string | null>;
        translate: p.Property<string | null>;
        unicode_bidi: p.Property<string | null>;
        user_select: p.Property<string | null>;
        vertical_align: p.Property<string | null>;
        visibility: p.Property<string | null>;
        white_space: p.Property<string | null>;
        widows: p.Property<string | null>;
        width: p.Property<string | null>;
        will_change: p.Property<string | null>;
        word_break: p.Property<string | null>;
        word_spacing: p.Property<string | null>;
        word_wrap: p.Property<string | null>;
        writing_mode: p.Property<string | null>;
        z_index: p.Property<string | null>;
    };
}
export interface Styles extends Styles.Attrs {
}
export declare class Styles extends Model {
    properties: Styles.Props;
    static __module__: string;
    constructor(attrs?: Partial<Styles.Attrs>);
}
//# sourceMappingURL=styles.d.ts.map