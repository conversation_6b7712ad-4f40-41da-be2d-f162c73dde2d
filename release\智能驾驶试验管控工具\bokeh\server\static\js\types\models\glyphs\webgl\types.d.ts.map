{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/lib/models/glyphs/webgl/types.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,aAAa,EAAE,qBAAqB,EAAE,WAAW,EAAC,MAAM,UAAU,CAAA;AAC1E,OAAO,EAAC,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,EAAC,MAAM,MAAM,CAAA;AAGxE,aAAK,WAAW,GAAG;IACjB,OAAO,EAAE,WAAW,CAAA;IACpB,QAAQ,EAAE,WAAW,CAAA;IACrB,WAAW,EAAE,IAAI,CAAA;IACjB,WAAW,EAAE,MAAM,CAAA;IACnB,SAAS,EAAE,MAAM,CAAA;CAClB,CAAA;AAED,aAAK,SAAS,GAAG;IACf,SAAS,EAAE,aAAa,CAAA;IACxB,UAAU,EAAE,qBAAqB,CAAA;IACjC,SAAS,EAAE,WAAW,CAAA;CACvB,CAAA;AAED,aAAK,eAAe,GAAG;IACrB,SAAS,EAAE,aAAa,CAAA;IACxB,UAAU,EAAE,qBAAqB,CAAA;CAClC,CAAA;AAED,aAAK,SAAS,GAAG;IACf,UAAU,EAAE,qBAAqB,CAAA;CAClC,CAAA;AAED,aAAK,SAAS,GAAG;IACf,aAAa,EAAE,aAAa,CAAA;IAC5B,QAAQ,EAAE,SAAS,CAAA;IACnB,aAAa,EAAE,MAAM,EAAE,CAAA;IACvB,UAAU,EAAE,MAAM,CAAA;IAClB,WAAW,EAAE,MAAM,CAAA;CACpB,CAAA;AAED,aAAK,UAAU,GAAG;IAChB,aAAa,EAAE,WAAW,CAAA;IAC1B,WAAW,EAAE,aAAa,CAAA;IAC1B,YAAY,EAAE,aAAa,CAAA;IAC3B,WAAW,EAAE,qBAAqB,CAAA;CACnC,CAAA;AAED,oBAAY,cAAc,GAAG,WAAW,GAAG;IACzC,UAAU,EAAE,MAAM,EAAE,CAAA;IACpB,SAAS,EAAE,MAAM,CAAA;IACjB,WAAW,EAAE,MAAM,CAAA;IACnB,MAAM,EAAE,aAAa,CAAA;IACrB,SAAS,EAAE,MAAM,CAAA;IACjB,QAAQ,EAAE,MAAM,CAAA;IAChB,SAAS,EAAE,MAAM,CAAA;CAClB,CAAA;AAED,oBAAY,kBAAkB,GAAG,cAAc,GAAG,SAAS,CAAA;AAE3D,oBAAY,gBAAgB,GAAG,WAAW,GAAG,eAAe,GAAG,SAAS,GAAG;IACzE,MAAM,EAAE,aAAa,CAAA;IACrB,QAAQ,EAAE,MAAM,CAAA;IAChB,IAAI,EAAE,aAAa,CAAA;IACnB,KAAK,EAAE,aAAa,CAAA;IACpB,IAAI,EAAE,WAAW,CAAA;CAClB,CAAA;AAED,oBAAY,cAAc,GAAG,WAAW,GAAG,SAAS,GAAG,SAAS,GAAG;IACjE,MAAM,EAAE,aAAa,CAAA;IACrB,QAAQ,EAAE,MAAM,CAAA;IAChB,KAAK,EAAE,aAAa,CAAA;IACpB,MAAM,EAAE,aAAa,CAAA;IACrB,KAAK,EAAE,aAAa,CAAA;IACpB,IAAI,EAAE,WAAW,CAAA;CAClB,CAAA;AAED,oBAAY,mBAAmB,GAAG,cAAc,GAAG,UAAU,CAAA;AAG7D,oBAAY,cAAc,GAAG;IAC3B,aAAa,EAAE,IAAI,CAAA;IACnB,aAAa,EAAE,MAAM,CAAA;IACrB,WAAW,EAAE,MAAM,CAAA;CACpB,CAAA;AAED,oBAAY,YAAY,GAAG;IACzB,UAAU,EAAE,SAAS,CAAA;IACrB,eAAe,EAAE,IAAI,CAAA;IACrB,YAAY,EAAE,MAAM,CAAA;IACpB,aAAa,EAAE,MAAM,CAAA;CACtB,CAAA;AAED,oBAAY,iBAAiB,GAAG,cAAc,GAAG;IAC/C,YAAY,EAAE,IAAI,CAAA;IAClB,WAAW,EAAE,MAAM,CAAA;IACnB,aAAa,EAAE,MAAM,CAAA;IACrB,WAAW,EAAE,MAAM,CAAA;IACnB,UAAU,EAAE,MAAM,CAAA;CACnB,CAAA;AAED,oBAAY,qBAAqB,GAAG,iBAAiB,GAAG,YAAY,CAAA;AAGpE,aAAK,cAAc,GAAG;IACpB,WAAW,EAAE,eAAe,CAAA;IAC5B,YAAY,EAAE,eAAe,CAAA;IAC7B,WAAW,EAAE,eAAe,CAAA;CAC7B,CAAA;AAED,aAAK,oBAAoB,GAAG;IAC1B,WAAW,EAAE,eAAe,CAAA;IAC5B,YAAY,EAAE,eAAe,CAAA;CAC9B,CAAA;AAED,aAAK,cAAc,GAAG;IACpB,YAAY,EAAE,eAAe,CAAA;CAC9B,CAAA;AAED,aAAK,eAAe,GAAG;IACrB,eAAe,EAAE,eAAe,CAAA;IAChC,aAAa,EAAE,eAAe,CAAA;IAC9B,cAAc,EAAE,eAAe,CAAA;IAC/B,aAAa,EAAE,eAAe,CAAA;CAC/B,CAAA;AAED,oBAAY,mBAAmB,GAAG;IAChC,UAAU,EAAE,eAAe,CAAA;IAC3B,YAAY,EAAE,eAAe,CAAA;IAC7B,aAAa,EAAE,eAAe,CAAA;IAC9B,WAAW,EAAE,eAAe,CAAA;IAC5B,YAAY,EAAE,eAAe,CAAA;CAC9B,CAAA;AAED,oBAAY,uBAAuB,GAAG,mBAAmB,GAAG;IAC1D,eAAe,EAAE,eAAe,CAAA;CACjC,CAAA;AAED,oBAAY,qBAAqB,GAAG,oBAAoB,GAAG,cAAc,GAAG;IAC1E,UAAU,EAAE,eAAe,CAAA;IAC3B,QAAQ,EAAE,eAAe,CAAA;IACzB,MAAM,EAAE,eAAe,CAAA;IACvB,OAAO,EAAE,eAAe,CAAA;IACxB,MAAM,EAAE,eAAe,CAAA;CACxB,CAAA;AAED,oBAAY,mBAAmB,GAAG,cAAc,GAAG,cAAc,GAAG;IAClE,QAAQ,EAAE,eAAe,CAAA;IACzB,MAAM,EAAE,eAAe,CAAA;IACvB,UAAU,EAAE,eAAe,CAAA;IAC3B,OAAO,EAAE,eAAe,CAAA;IACxB,QAAQ,EAAE,eAAe,CAAA;IACzB,OAAO,EAAE,eAAe,CAAA;CACzB,CAAA;AAED,oBAAY,wBAAwB,GAAG,mBAAmB,GAAG,eAAe,CAAA"}