{"version": 3, "file": "plot_canvas.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/models/plots/plot_canvas.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,cAAc,EAAC,MAAM,2BAA2B,CAAA;AACxD,OAAO,EAAS,UAAU,EAAE,QAAQ,EAAC,MAAM,kBAAkB,CAAA;AAC7D,OAAO,EAAC,QAAQ,EAAE,YAAY,EAAC,MAAM,uBAAuB,CAAA;AAC5D,OAAO,EAAC,YAAY,EAAC,MAAM,4BAA4B,CAAA;AACvD,OAAO,EAAC,IAAI,EAAE,QAAQ,EAAC,MAAM,eAAe,CAAA;AAC5C,OAAO,EAAC,SAAS,EAAC,MAAM,yBAAyB,CAAA;AACjD,OAAO,EAAC,SAAS,EAAE,aAAa,EAAC,MAAM,uBAAuB,CAAA;AAC9D,OAAO,EAAC,IAAI,EAAC,MAAM,QAAQ,CAAA;AAE3B,OAAO,EAAC,KAAK,EAAC,MAAM,sBAAsB,CAAA;AAC1C,OAAO,EAAO,QAAQ,EAAC,MAAM,cAAc,CAAA;AAC3C,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAA;AAIzD,OAAO,EAAU,UAAU,EAAC,2BAAoB;AAGhD,OAAO,EAAO,WAAW,EAAC,yBAAkB;AAC5C,OAAO,EAAC,iBAAiB,EAAC,wBAAiB;AAI3C,OAAO,EAAC,SAAS,EAAE,WAAW,EAAC,+BAAwB;AACvD,OAAO,EAAC,YAAY,EAAE,UAAU,EAAC,0BAAmB;AAEpD,OAAO,EAAC,YAAY,EAAC,iCAA0B;AAG/C,OAAO,EAAC,IAAI,EAAC,6BAAsB;AACnC,OAAO,EAAC,SAAS,EAAE,YAAY,EAAE,YAAY,EAAC,MAAM,iBAAiB,CAAA;AACrE,OAAO,EAAC,SAAS,EAAE,YAAY,EAAC,MAAM,iBAAiB,CAAA;AAGvD,qBAAa,QAAS,SAAQ,aAAc,YAAW,UAAU;IACtD,KAAK,EAAE,IAAI,CAAA;IACpB,OAAO,EAAE,IAAI,CAAC,OAAO,CAAA;IAEZ,MAAM,EAAE,YAAY,CAAA;IAE7B,KAAK,EAAE,cAAc,CAAA;IAErB,WAAW,EAAE,UAAU,CAAA;IACvB,IAAI,MAAM,IAAI,UAAU,CAEvB;IAED,SAAS,CAAC,MAAM,EAAE,KAAK,CAAA;IACvB,SAAS,CAAC,QAAQ,EAAE,YAAY,CAAA;IAEhC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAa;IACxC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAa;IACxC,SAAS,CAAC,YAAY,EAAE,OAAO,CAAO;IACtC,SAAS,CAAC,aAAa,EAAE,OAAO,CAAQ;IACxC,SAAS,CAAC,qBAAqB,EAAE,GAAG,CAAC,YAAY,CAAC,CAAY;IAC9D,SAAS,CAAC,eAAe,EAAE,OAAO,CAAO;IAEzC,SAAS,CAAC,cAAc,EAAE,YAAY,CAAA;IACtC,SAAS,CAAC,cAAc,EAAE,YAAY,CAAA;IAEtC,IAAI,KAAK,IAAI,YAAY,CAExB;IAED,IAAI,qBAAqB,CAAC,KAAK,EAAE,OAAO,EAEvC;IAED,oBAAoB,EAAE,CAAC,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,CAAC,EAAE,CAAA;IAEpD,SAAS,CAAC,UAAU,CAAC,EAAE,MAAM,CAAA;IAE7B,SAAS,CAAC,WAAW,EAAE,OAAO,CAAA;IAE9B,SAAS,CAAC,cAAc,EAAE,SAAS,CAAA;IAEnC,SAAS,CAAC,eAAe,EAAE,MAAM,IAAI,CAAA;IAErC,kBAAkB,EAAE,QAAQ,EAAE,CAAA;IAE9B,aAAa,CAAC,CAAC,SAAS,QAAQ,EAAE,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,eAAe,CAAC,GAAG,SAAS;IAYhE,cAAc,EAAE,GAAG,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAA;IAC3C,UAAU,EAAE,GAAG,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAA;IAE7C,IAAI,SAAS,IAAI,OAAO,CAEvB;IAED,IAAI,YAAY,IAAI,SAAS,EAAE,CAE9B;IAED,KAAK,IAAI,IAAI;IAOb,OAAO,CAAC,SAAS,GAAE,OAAe,GAAG,IAAI;IASzC,OAAO,CAAC,aAAa,CAAiB;IACtC,2BAA2B,IAAI,IAAI;IAKnC,cAAc,IAAI,IAAI;IAItB,aAAa,CAAC,aAAa,EAAE,YAAY,EAAE,GAAG,YAAY,GAAG,YAAY,GAAG,IAAI;IAKhF,mBAAmB,CAAC,aAAa,EAAE,YAAY,EAAE,GAAG,YAAY,GAAG,YAAY,GAAG,IAAI;IAUtF,cAAc,IAAI,IAAI;IAOtB,cAAc,IAAI,IAAI;IAKtB,KAAK,IAAI,IAAI;IASJ,MAAM,IAAI,IAAI;IAOd,MAAM,IAAI,IAAI;IAOd,UAAU,IAAI,IAAI;IA6CZ,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC;cAiB5B,aAAa,IAAI,YAAY;cAI7B,cAAc,IAAI,YAAY;IAIxC,cAAc,IAAI,IAAI;IAgJ/B,IAAI,UAAU,IAAI,QAAQ,EAAE,CAO3B;IAED,sBAAsB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IAK9C,YAAY,CAAC,UAAU,EAAE,SAAS,GAAG,IAAI,EAAE,OAAO,CAAC,EAAE,YAAY,GAAG,IAAI;IAMxE,WAAW,IAAI,IAAI;IAKnB,2BAA2B,IAAI,IAAI;IAKnC,aAAa,IAAI,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC;IAS7C,gBAAgB,CAAC,UAAU,EAAE,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,GAAG,IAAI,GAAG,IAAI;IAavE,eAAe,IAAI,IAAI;IAIvB,SAAS,CAAC,kBAAkB,IAAI,IAAI;IAgBpC,kBAAkB,IAAI,YAAY,EAAE;IAIpC,SAAS,CAAE,kBAAkB,IAAI,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,SAAS,CAAC;IAwB/D,oBAAoB,IAAI,OAAO,CAAC,IAAI,CAAC;IAKrC,gBAAgB,IAAI,OAAO,CAAC,IAAI,CAAC;IAM9B,eAAe,IAAI,IAAI;IAwBvB,YAAY,IAAI,OAAO;IAcvB,YAAY,IAAI,IAAI;IA6C7B,OAAO,IAAI,IAAI;IAMf,KAAK,IAAI,IAAI;IAeb,SAAS,CAAC,aAAa,IAAI,IAAI;IAgF/B,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,GAAG,IAAI;IAuB9G,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU;IAa1D,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,GAAG,IAAI;IAEhE,SAAS,CAAC,YAAY,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,GAAG,IAAI;IAgBjE,SAAS,CAAC,cAAc,CAAC,GAAG,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,GAAG,IAAI;IAkBnE,OAAO,IAAI,OAAO,CAAC,IAAI,CAAC;IAIf,MAAM,CAAC,IAAI,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,GAAE,OAAc,GAAG,WAAW;IAa/D,kBAAkB,IAAI,iBAAiB;CAOjD"}