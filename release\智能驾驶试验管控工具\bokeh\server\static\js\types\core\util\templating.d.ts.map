{"version": 3, "file": "templating.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/core/util/templating.ts"], "names": [], "mappings": "AAOA,OAAO,EAAC,kBAAkB,EAAC,kDAA2C;AACtE,OAAO,EAAC,aAAa,EAAC,qDAA8C;AACpE,OAAO,EAAC,UAAU,EAAC,0CAAmC;AAEtD,eAAO,MAAM,aAAa,kEAAwC,CAAA;AAClE,oBAAY,aAAa,GAAG,SAAS,GAAG,QAAQ,GAAG,UAAU,CAAA;AAE7D,oBAAY,aAAa,GAAG,aAAa,GAAG,aAAa,CAAA;AACzD,oBAAY,UAAU,GAAG;IAAC,CAAC,GAAG,EAAE,MAAM,GAAG,aAAa,CAAA;CAAC,CAAA;AACvD,oBAAY,aAAa,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,IAAI,KAAK,MAAM,CAAA;AAC1F,oBAAY,KAAK,GAAG,MAAM,GAAG,UAAU,CAAA;AACvC,oBAAY,IAAI,GAAG;IAAC,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAA;CAAC,CAAA;AAE3C,eAAO,MAAM,kBAAkB;qBACX,OAAO,UAAU,MAAM,iBAAiB,IAAI;sBAC5C,OAAO,UAAU,MAAM,iBAAiB,IAAI;oBAC5C,OAAO,UAAU,MAAM,iBAAiB,IAAI;CAC/D,CAAA;AAED,wBAAgB,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,EAAE,OAAO,EAAE,GAAG,MAAM,CAElE;AAED,wBAAgB,eAAe,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,aAAa,EAAE,IAAI,GAAG,MAAM,CAgB5F;AAED,wBAAgB,aAAa,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,EAAE,UAAU,GAAG,aAAa,CAuBvG;AASD,wBAAgB,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC,EAAE,KAAK,GAAG,OAAO,GAAG,IAAI,CAsBzG;AAED,wBAAgB,SAAS,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,CAAC,EAAE,KAAK,EAAE,YAAY,EAAE,IAAI,WAQxG;AAED,wBAAgB,oBAAoB,CAAC,OAAO,EAAE,MAAM,GAAG;IAAC,IAAI,EAAE,MAAM,CAAA;CAAC,EAAE,WAAW,EAAE,kBAAkB,EAClG,CAAC,EAAE,KAAK,EAAE,UAAU,CAAC,EAAE,UAAU,EAAE,YAAY,GAAE,IAAS,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,MAAM,KAAK,MAAM,GAAG,MAAM,GAAG,IAAI,EAAE,CAgD/G"}