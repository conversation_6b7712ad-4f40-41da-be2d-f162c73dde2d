{"version": 3, "file": "axis.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/models/axes/axis.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,aAAa,EAAE,iBAAiB,EAAC,MAAM,6BAA6B,CAAA;AAC5E,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAA;AACxC,OAAO,EAAC,aAAa,EAAC,MAAM,8BAA8B,CAAA;AAC1D,OAAO,EAAC,cAAc,EAA6B,MAAM,sBAAsB,CAAA;AAC/E,OAAO,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAA;AACrC,OAAO,KAAK,OAAO,2BAAoB;AACvC,OAAO,KAAK,MAAM,mCAA4B;AAC9C,OAAO,KAAK,CAAC,8BAAuB;AACpC,OAAO,EAAC,iBAAiB,EAAC,wBAAiB;AAC3C,OAAO,EAAC,IAAI,EAAE,oBAAoB,EAAC,yBAAkB;AACrD,OAAO,EAAC,IAAI,EAAE,UAAU,EAAC,0BAAmB;AAE5C,OAAO,EAAC,KAAK,EAAc,MAAM,EAAC,qCAA8B;AAChE,OAAO,EAAC,SAAS,EAAC,+BAAwB;AAI1C,OAAO,EAAC,aAAa,EAAU,4BAAqB;AACpD,OAAO,EAAC,MAAM,EAAc,+BAAkC;AAC9D,OAAO,EAAC,YAAY,EAAC,MAAM,mBAAmB,CAAA;AAC9C,OAAO,EAAC,QAAQ,EAAC,MAAM,mBAAmB,CAAA;AAQ1C,oBAAY,OAAO,GAAG;IACpB,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,EAAE,CAAA;IACrB,UAAU,EAAE,MAAM,CAAA;IAClB,UAAU,EAAE,MAAM,CAAA;CACnB,CAAA;AAED,oBAAY,MAAM,GAAG,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,CAAC,CAAA;AAEzC,MAAM,WAAW,UAAU;IACzB,KAAK,EAAE,MAAM,CAAA;IACb,KAAK,EAAE,MAAM,CAAA;CACd;AAED,qBAAa,QAAS,SAAQ,iBAAiB;IACpC,KAAK,EAAE,IAAI,CAAA;IACX,OAAO,EAAE,IAAI,CAAC,OAAO,CAAA;IAE9B,KAAK,EAAE,KAAK,CAAA;IACZ,MAAM,EAAE,UAAU,CAAA;IAEN,gBAAgB,EAAE,YAAY,GAAG,IAAI,CAAO;IAC5C,kBAAkB,EAAE,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC,CAAY;IAEtD,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC;cAO/B,gBAAgB,IAAI,OAAO,CAAC,IAAI,CAAC;cASjC,kBAAkB,IAAI,OAAO,CAAC,IAAI,CAAC;IAQnD,aAAa,IAAI,IAAI;IAKrB,QAAQ,IAAI,IAAI;IAUhB,IAAI,aAAa,IAAI,OAAO,CAG3B;IAED,SAAS,CAAC,OAAO,IAAI,IAAI;IAiBzB,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI;IAEzE,eAAe,IAAI,IAAI;IAoBhC,IAAa,UAAU,IAAI,OAAO,CAEjC;IAID,SAAS,CAAC,UAAU,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,GAAG,IAAI;IAoB7D,SAAS,CAAC,iBAAiB,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI;IAQ7F,SAAS,CAAC,iBAAiB,CAAC,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI;IAQ7F,SAAS,CAAC,kBAAkB,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,WAAW,EAAE,UAAU,GAAG,IAAI;IAU7F,SAAS,CAAC,kBAAkB,IAAI,MAAM;IAqBtC,SAAS,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,GAAG,IAAI;IA0C5F,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,IAAI,GAAG,IAAI;IA0B7G,SAAS,CAAC,qBAAqB,CAAC,GAAG,EAAE,SAAS,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,EACrD,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EACtD,OAAO,EAAE,OAAO,CAAC,IAAI,GAAG,IAAI;IAsG9C,YAAY,IAAI,MAAM;IAIpC,SAAS,CAAC,mBAAmB,IAAI,MAAM,EAAE;IAWzC,IAAI,OAAO,IAAI,OAAO,CAQrB;IAED,SAAS,CAAC,uBAAuB,CAAC,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC,IAAI,GAAG,MAAM;IAgBlI,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAE9B;IAED,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,CAErB;IAED,cAAc,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,aAAa;IAuB9C,IAAI,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAyB9B;IAED,IAAI,MAAM,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAK3B;IAED,IAAI,eAAe,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CA0BtC;IAED,IAAI,WAAW,IAAI,MAAM,CAmBxB;IAED,IAAI,WAAW,IAAI,UAAU,CAsC5B;IAED,IAAI,GAAG,IAAI,MAAM,CAuBhB;IAGQ,kBAAkB,IAAI,iBAAiB;IAOvC,MAAM,IAAI,IAAI;IAUd,YAAY,IAAI,OAAO;CAgBjC;AAED,yBAAiB,IAAI,CAAC;IACpB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,aAAa,CAAC,KAAK,GAAG;QACxC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,MAAM,CAAC,CAAA;QAC7C,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC1B,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;QACpC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,GAAG,IAAI,CAAC,CAAA;QAChD,mBAAmB,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACvC,oBAAoB,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACxC,uBAAuB,EAAE,CAAC,CAAC,QAAQ,CAAC,oBAAoB,GAAG,MAAM,CAAC,CAAA;QAClE,qBAAqB,EAAE,CAAC,CAAC,QAAQ,CAAC;YAAC,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,QAAQ,CAAA;SAAC,CAAC,CAAA;QACrE,kBAAkB,EAAE,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAA;QAC9C,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACjC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAClC,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACjC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAClC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,GAAG,IAAI,CAAC,CAAA;KACnD,GAAG,MAAM,CAAA;IAEV,KAAY,MAAM,GAChB,MAAM,CAAC,QAAQ,GACf,MAAM,CAAC,aAAa,GACpB,MAAM,CAAC,aAAa,GACpB,MAAM,CAAC,cAAc,GACrB,MAAM,CAAC,aAAa,CAAA;IAEtB,KAAY,OAAO,GAAG,aAAa,CAAC,OAAO,GAAG;QAC5C,SAAS,EAAE,OAAO,CAAC,IAAI,CAAA;QACvB,eAAe,EAAE,OAAO,CAAC,IAAI,CAAA;QAC7B,eAAe,EAAE,OAAO,CAAC,IAAI,CAAA;QAC7B,gBAAgB,EAAE,OAAO,CAAC,IAAI,CAAA;QAC9B,eAAe,EAAE,OAAO,CAAC,IAAI,CAAA;KAC9B,CAAA;CACF;AAED,MAAM,WAAW,IAAK,SAAQ,IAAI,CAAC,KAAK;CAAG;AAE3C,qBAAa,IAAK,SAAQ,aAAa;IAC5B,UAAU,EAAE,IAAI,CAAC,KAAK,CAAA;IACtB,aAAa,EAAE,QAAQ,CAAA;gBAEpB,KAAK,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;CA8CxC"}