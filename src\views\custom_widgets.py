from PyQt5.QtWidgets import (
    QComboBox, QStyledItemDelegate, QListView, QCheckBox, QWidget, 
    QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QFrame, QApplication,
    QSizeGrip
)
from PyQt5.QtCore import Qt, pyqtSignal, QEvent, QSortFilterProxyModel, QRegExp, QPoint, QSize
from PyQt5.QtGui import QStandardItemModel, QStandardItem, QCursor

class CheckableItemDelegate(QStyledItemDelegate):
    """自定义代理，用于在ComboBox的下拉列表中显示复选框"""
    def paint(self, painter, option, index):
        # 使用默认绘制方法
        super().paint(painter, option, index)

class SearchableListView(QFrame):
    """自定义的可搜索列表视图"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置窗口样式
        self.setWindowFlags(Qt.Popup | Qt.FramelessWindowHint)
        self.setFrameStyle(QFrame.StyledPanel)
        
        # 创建布局
        self.layout = QVBoxLayout(self)
        self.layout.setContentsMargins(5, 5, 5, 5)
        self.layout.setSpacing(0)
        
        # 创建搜索框
        self.search_box = QLineEdit()
        self.search_box.setPlaceholderText("搜索包含多个关键字，空格分隔")
        self.search_box.setClearButtonEnabled(True)
        self.layout.addWidget(self.search_box)
        
        # 创建列表视图
        self.list_view = QListView()
        self.layout.addWidget(self.list_view)
        
        # 创建代理模型用于筛选
        self.proxy_model = QSortFilterProxyModel()
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseInsensitive)
        
        # 添加大小调整控件
        self.size_grip = QSizeGrip(self)
        self.size_grip.setVisible(True)
        
       # 设置大小调整控件的样式
        # self.size_grip.setStyleSheet("""
        #     QSizeGrip {
        #         background-color: #c0c0c0;
        #         width: 16px;
        #         height: 16px;
        #         margin: 0px;
        #         border-top-left-radius: 8px;
        #     }
        #     QSizeGrip:hover {
        #         background-color: #a0a0a0;
        #     }
        # """)
        ###
        # 设置最小尺寸
        self.setMinimumSize(200, 150)
        
        # 记录调整大小的状态
        self.resizing = False
        self.resize_start_pos = QPoint()
        self.resize_start_size = QSize()
        
        # 连接信号
        self.search_box.textChanged.connect(self.filter_items)
        
    def resizeEvent(self, event):
        """重写调整大小事件，更新大小调整控件位置"""
        super().resizeEvent(event)
        # 更新大小调整控件位置
        self.size_grip.move(self.width() - self.size_grip.width(),
                           self.height() - self.size_grip.height())
        
    def set_model(self, model):
        """设置模型"""
        self.proxy_model.setSourceModel(model)
        self.list_view.setModel(self.proxy_model)
        
    def filter_items(self, text):
        """根据搜索文本过滤项目"""
        if not text:
            # 如果搜索框为空，显示所有项
            self.proxy_model.setFilterRegExp("")
            return
            
        # 分割搜索关键词
        keywords = text.strip().split()
        if not keywords:
            self.proxy_model.setFilterRegExp("")
            return
            
        # 创建正则表达式，匹配所有关键词（AND逻辑）
        regex_pattern = "^"
        for keyword in keywords:
            regex_pattern += f"(?=.*{keyword})"
        regex_pattern += ".*$"
        
        # 设置过滤正则表达式
        self.proxy_model.setFilterRegExp(QRegExp(regex_pattern, Qt.CaseInsensitive))
        
    def adjust_size(self):
        """调整窗口大小"""
        width = max(self.list_view.sizeHint().width(), 250)
        height = self.search_box.sizeHint().height() + min(self.list_view.sizeHint().height(), 300)
        self.resize(width, height)
        
    def showEvent(self, event):
        """显示事件处理"""
        super().showEvent(event)
        self.search_box.clear()
        self.search_box.setFocus()
        
    def focusOutEvent(self, event):
        """失去焦点时隐藏"""
        # 检查是否是子控件获得了焦点
        focus_widget = QApplication.focusWidget()
        if focus_widget is not None and (focus_widget is self or self.isAncestorOf(focus_widget)):
            # 如果是子控件获得了焦点，不隐藏
            pass
        else:
            # 否则隐藏弹出窗口
            self.hide()
        super().focusOutEvent(event)

class SearchableComboBox(QComboBox):
    """可搜索的下拉框组件，支持模糊匹配"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置为可编辑
        self.setEditable(True)
        self.setInsertPolicy(QComboBox.NoInsert)

        # 连接信号
        self.lineEdit().textChanged.connect(self.filter_items)
        self.activated.connect(self.on_item_selected)

        # 存储原始项目列表和当前选择的值
        self.original_items = []
        self.selected_value = ""
        self._updating_items = False
        self._ignore_text_change = False

    def addItem(self, text, userData=None):
        """添加项目"""
        super().addItem(text, userData)
        # 只有在非更新状态下才修改原始项目列表
        if not self._updating_items:
            self.original_items.append(text)

    def addItems(self, texts):
        """添加多个项目"""
        super().addItems(texts)
        # 只有在非更新状态下才修改原始项目列表
        if not self._updating_items:
            self.original_items.extend(texts)

    def clear(self):
        """清空所有项目"""
        super().clear()
        # 只有在非更新状态下才清空原始项目列表
        if not self._updating_items:
            self.original_items.clear()

    def filter_items(self, text):
        """根据输入文本过滤项目"""
        if self._updating_items or self._ignore_text_change:
            return

        if not text:
            # 如果输入为空，显示所有项目
            self._update_items(self.original_items)
            return

        # 过滤匹配的项目
        text_lower = text.lower()

        # 按相关性排序：完全匹配 > 前缀匹配 > 包含匹配
        exact_matches = []
        prefix_matches = []
        contains_matches = []

        for item in self.original_items:
            if not item:  # 跳过空字符串
                continue
            item_lower = item.lower()
            if item_lower == text_lower:
                exact_matches.append(item)
            elif item_lower.startswith(text_lower):
                prefix_matches.append(item)
            elif text_lower in item_lower:
                contains_matches.append(item)

        # 合并结果
        filtered_items = exact_matches + prefix_matches + contains_matches

        # 更新下拉框内容
        if filtered_items:
            self._update_items(filtered_items)
        else:
            # 没有匹配项时清空输入框并显示完整列表
            self._clear_invalid_input()

    def _update_items(self, items):
        """更新下拉框项目"""
        if self._updating_items:
            return

        self._updating_items = True
        self._ignore_text_change = True

        try:
            # 保存当前文本
            current_text = self.lineEdit().text()

            # 清空并添加新项目
            self.clear()
            for item in items:
                self.addItem(item)

            # 恢复文本
            self.lineEdit().setText(current_text)

        finally:
            self._ignore_text_change = False
            self._updating_items = False

    def _clear_invalid_input(self):
        """清空无效输入并恢复完整列表"""
        self._updating_items = True
        self._ignore_text_change = True

        try:
            # 清空输入框
            self.lineEdit().clear()

            # 恢复完整列表
            self.clear()
            for item in self.original_items:
                self.addItem(item)

            # 如果之前有选择的值，恢复它
            if self.selected_value and self.selected_value in self.original_items:
                self.setCurrentText(self.selected_value)

        finally:
            self._ignore_text_change = False
            self._updating_items = False

    def on_item_selected(self, index):
        """当用户选择项目时的处理"""
        if self._updating_items:
            return

        text = self.itemText(index)
        if text and text != "无匹配项" and text in self.original_items:
            self.selected_value = text
            self._restore_full_list_with_selection(text)

    def _restore_full_list_with_selection(self, selected_text):
        """恢复完整列表并设置选择的文本"""
        self._updating_items = True
        self._ignore_text_change = True

        try:
            # 恢复完整列表
            self.clear()
            for item in self.original_items:
                self.addItem(item)

            # 设置选择的文本
            self.setCurrentText(selected_text)

        finally:
            self._ignore_text_change = False
            self._updating_items = False

    def setCurrentText(self, text):
        """设置当前文本"""
        if self._updating_items:
            super().setCurrentText(text)
            return

        self._updating_items = True
        self._ignore_text_change = True

        try:
            # 如果文本在原始项目中或为空，显示完整列表
            if text in self.original_items or text == "":
                self.clear()
                for item in self.original_items:
                    self.addItem(item)
                super().setCurrentText(text)
                if text in self.original_items:
                    self.selected_value = text
            else:
                # 如果文本不在列表中，清空并显示完整列表
                self.clear()
                for item in self.original_items:
                    self.addItem(item)
                super().setCurrentText("")

        finally:
            self._ignore_text_change = False
            self._updating_items = False

    def focusOutEvent(self, event):
        """失去焦点时的处理"""
        current_text = self.lineEdit().text()

        # 如果当前文本不在原始项目中，清空输入并恢复完整列表
        if current_text and current_text not in self.original_items:
            self._clear_invalid_input()
        elif current_text in self.original_items:
            # 如果是有效选择，确保显示完整列表
            self.selected_value = current_text
            self._restore_full_list_with_selection(current_text)

        super().focusOutEvent(event)


class MultiSelectComboBox(QComboBox):
    """支持多选的下拉框组件，带搜索功能"""

    # 定义信号，当选择项变化时发出
    selectionChanged = pyqtSignal()

    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置模型
        self.model = QStandardItemModel()
        self.setModel(self.model)

        # 创建自定义弹出窗口
        self.popup = SearchableListView(self)
        self.popup.set_model(self.model)

        # 设置代理
        self.delegate = CheckableItemDelegate()
        self.popup.list_view.setItemDelegate(self.delegate)

        # 添加"全部"选项
        self.addItem("全部")
        self.all_item = self.model.item(0)
        self.all_item.setCheckState(Qt.Checked)

        # 连接信号
        self.popup.list_view.pressed.connect(self.handleItemPressed)
        self.model.dataChanged.connect(self.updateText)

        # 标记是否正在处理选择变化，避免递归
        self._handling_change = False

        # 保存用户设置的大小
        self.saved_popup_size = QSize(300, 350)  # 默认大小

        # 安装事件过滤器
        QApplication.instance().installEventFilter(self)
        
    def eventFilter(self, obj, event):
        """事件过滤器，处理点击事件"""
        if event.type() == QEvent.MouseButtonPress and self.popup.isVisible():
            # 如果点击发生在弹出窗口外部
            if not self.popup.geometry().contains(event.globalPos()):
                # 如果点击发生在下拉框上，不做处理（让showPopup处理）
                if self.geometry().contains(self.mapFromGlobal(event.globalPos())):
                    return super().eventFilter(obj, event)
                # 否则隐藏弹出窗口
                self.popup.hide()
                
        return super().eventFilter(obj, event)
        
    def addItem(self, text, userData=None):
        """添加带复选框的项"""
        item = QStandardItem(text)
        item.setCheckState(Qt.Unchecked)
        item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsUserCheckable | Qt.ItemIsSelectable)
        
        if userData is not None:
            item.setData(userData, Qt.UserRole)
            
        self.model.appendRow(item)
        
    def addItems(self, texts):
        """批量添加项，优化性能"""
        if not texts:
            return

        # 暂停信号发射，避免频繁更新
        self.model.blockSignals(True)

        try:
            # 批量创建项目
            items = []
            for text in texts:
                item = QStandardItem(text)
                item.setCheckState(Qt.Unchecked)
                item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsUserCheckable | Qt.ItemIsSelectable)
                items.append(item)

            # 批量添加到模型
            for item in items:
                self.model.appendRow(item)

        finally:
            # 恢复信号发射
            self.model.blockSignals(False)

        # 手动触发一次更新
        self.updateText()
            
    def handleItemPressed(self, index):
        """处理项被点击的事件"""
        if self._handling_change:
            return
            
        self._handling_change = True
        
        # 从代理模型获取源模型的索引
        source_index = self.popup.proxy_model.mapToSource(index)
        item = self.model.itemFromIndex(source_index)
        
        # 如果点击的是"全部"选项
        if source_index.row() == 0:
            new_state = Qt.Checked if item.checkState() == Qt.Unchecked else Qt.Unchecked
            item.setCheckState(new_state)
            
            # 如果选中"全部"，则取消选中其他项
            if new_state == Qt.Checked:
                for i in range(1, self.model.rowCount()):
                    self.model.item(i).setCheckState(Qt.Unchecked)
        else:
            # 如果点击的是其他选项
            new_state = Qt.Checked if item.checkState() == Qt.Unchecked else Qt.Unchecked
            item.setCheckState(new_state)
            
            # 如果选中了任何其他选项，则取消选中"全部"
            if new_state == Qt.Checked:
                self.all_item.setCheckState(Qt.Unchecked)
            
            # 如果没有任何选项被选中，则选中"全部"
            any_checked = False
            for i in range(1, self.model.rowCount()):
                if self.model.item(i).checkState() == Qt.Checked:
                    any_checked = True
                    break
                    
            if not any_checked:
                self.all_item.setCheckState(Qt.Checked)
                
        self.updateText()
        self.selectionChanged.emit()
        self._handling_change = False
        
    def updateText(self):
        """更新显示的文本"""
        if self.all_item.checkState() == Qt.Checked:
            self.setCurrentText("全部")
            return
            
        selected_texts = []
        for i in range(1, self.model.rowCount()):
            item = self.model.item(i)
            if item.checkState() == Qt.Checked:
                selected_texts.append(item.text())
                
        if selected_texts:
            # 如果选中项太多，则显示选中数量
            if len(selected_texts) > 2:
                self.setCurrentText(f"已选择 {len(selected_texts)} 项")
            else:
                self.setCurrentText(", ".join(selected_texts))
        else:
            self.setCurrentText("全部")
            self.all_item.setCheckState(Qt.Checked)
            
    def getSelectedItems(self):
        """获取所有选中的项"""
        selected_items = []
        
        # 如果"全部"被选中，返回空列表表示全选
        if self.all_item.checkState() == Qt.Checked:
            return []
            
        for i in range(1, self.model.rowCount()):
            item = self.model.item(i)
            if item.checkState() == Qt.Checked:
                selected_items.append(item.text())
                
        return selected_items
        
    def selectAll(self):
        """选择所有项"""
        self.all_item.setCheckState(Qt.Checked)
        for i in range(1, self.model.rowCount()):
            self.model.item(i).setCheckState(Qt.Unchecked)
        self.updateText()
        self.selectionChanged.emit()
        
    def clearSelection(self):
        """清除所有选择"""
        self.all_item.setCheckState(Qt.Unchecked)
        for i in range(1, self.model.rowCount()):
            self.model.item(i).setCheckState(Qt.Unchecked)
        self.updateText()
        self.selectionChanged.emit()
        
    def selectItems(self, items):
        """选择指定的项"""
        if not items:
            self.selectAll()
            return
            
        self.all_item.setCheckState(Qt.Unchecked)
        for i in range(1, self.model.rowCount()):
            item = self.model.item(i)
            if item.text() in items:
                item.setCheckState(Qt.Checked)
            else:
                item.setCheckState(Qt.Unchecked)
        self.updateText()
        self.selectionChanged.emit()
        
    def showPopup(self):
        """显示自定义弹出窗口"""
        # 使用保存的大小
        if self.saved_popup_size.isValid():
            self.popup.resize(self.saved_popup_size)
        else:
            # 如果没有保存的大小，则调整为默认大小
            self.popup.adjust_size()
        
        # 计算弹出窗口位置
        pos = self.mapToGlobal(self.rect().bottomLeft())
        self.popup.move(pos)
        
        # 显示弹出窗口
        self.popup.show()
        
        # 确保搜索框获取焦点
        self.popup.search_box.setFocus()
        
    def hidePopup(self):
        """隐藏自定义弹出窗口"""
        # 保存当前大小
        if self.popup.isVisible():
            self.saved_popup_size = self.popup.size()
            
        self.popup.hide()
        
        # 清空搜索框并重置搜索结果
        self.popup.search_box.clear()
        self.popup.filter_items("")
        
    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_Escape:
            self.hidePopup()
        else:
            super().keyPressEvent(event) 