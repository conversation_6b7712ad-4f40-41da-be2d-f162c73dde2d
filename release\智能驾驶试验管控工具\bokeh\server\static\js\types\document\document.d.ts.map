{"version": 3, "file": "document.d.ts", "sourceRoot": "", "sources": ["../../../../src/lib/document/document.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,aAAa,EAAC,MAAM,SAAS,CAAA;AAGrC,OAAO,EAAC,UAAU,EAAiB,UAAU,EAAmB,6BAAyB;AACzF,OAAO,EAAC,QAAQ,EAAC,0BAAsB;AAEvC,OAAO,EAAC,EAAE,EAAE,KAAK,EAAoB,sBAAkB;AACvD,OAAO,EAAC,OAAO,EAAC,0BAAsB;AACtC,OAAO,EAAC,MAAM,EAAS,0BAAsB;AAC7C,OAAO,EAAC,OAAO,EAAiC,mCAA+B;AAO/E,OAAO,EAAC,SAAS,EAAC,qCAAiC;AAEnD,OAAO,EAAC,aAAa,EAAC,0BAAsB;AAC5C,OAAO,EAAC,KAAK,EAAC,iBAAa;AAC3B,OAAO,EAAC,QAAQ,EAAe,MAAM,QAAQ,CAAA;AAC7C,OAAO,EACL,aAAa,EAAsB,oBAAoB,EAEvD,eAAe,EAAE,YAAY,EAC9B,MAAM,UAAU,CAAA;AAGjB,qBAAa,YAAY;IAIX,QAAQ,CAAC,QAAQ,EAAE,QAAQ;IAHvC,OAAO,EAAE,aAAa,GAAG,IAAI,CAAO;IACpC,iBAAiB,EAAE,GAAG,CAAC,KAAK,CAAC,CAAY;gBAEpB,QAAQ,EAAE,QAAQ;IAEvC,UAAU,CAAC,WAAW,EAAE,UAAU,GAAG,IAAI;IAKzC,OAAO,CAAC,KAAK,EAAE,UAAU,GAAG,IAAI;CAOjC;AAED,oBAAY,OAAO,GAAG;IACpB,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAA;IACjB,KAAK,EAAE;QACL,QAAQ,EAAE,EAAE,EAAE,CAAA;QACd,UAAU,EAAE,MAAM,EAAE,CAAA;KACrB,CAAA;CACF,CAAA;AAED,oBAAY,KAAK,GAAG;IAClB,UAAU,EAAE,MAAM,EAAE,CAAA;IACpB,MAAM,EAAE,eAAe,EAAE,CAAA;CAC1B,CAAA;AAED,oBAAY,MAAM,GAAG,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;AAEtC,eAAO,MAAM,SAAS,EAAE,QAAQ,EAAO,CAAA;AAEvC,eAAO,MAAM,aAAa,sBAAsB,CAAA;AAIhD,qBAAa,QAAQ;IACnB,QAAQ,CAAC,aAAa,EAAE,YAAY,CAAA;IACpC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;IAE5B,SAAS,CAAC,QAAQ,CAAC,eAAe,EAAE,MAAM,CAAA;IAC1C,SAAS,CAAC,QAAQ,CAAC,SAAS,EAAE,aAAa,CAAA;IAC3C,SAAS,CAAC,MAAM,EAAE,MAAM,CAAA;IACxB,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,CAAA;IACX,WAAW,EAAE,GAAG,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAA;IAC5C,SAAS,CAAC,wBAAwB,EAAE,MAAM,CAAA;IAC1C,SAAS,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,aAAa,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,oBAAoB,KAAK,IAAI,CAAC,EAAE,OAAO,CAAC,CAAA;IAC9G,SAAS,CAAC,kBAAkB,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,OAAO,KAAK,IAAI,CAAC,CAAC,CAAA;IACvE,OAAO,CAAC,WAAW,CAA4B;IAC/C,SAAS,CAAC,sBAAsB,EAAE,MAAM,GAAG,IAAI,CAAA;IAC/C,SAAS,CAAC,iBAAiB,EAAE,KAAK,GAAG,IAAI,CAAA;IACzC,SAAS,CAAC,qBAAqB,EAAE,CAAC,MAAM,IAAI,CAAC,GAAE,IAAI,CAAA;gBAEvC,OAAO,CAAC,EAAE;QAAC,QAAQ,CAAC,EAAE,aAAa,CAAA;KAAC;IAiBhD,IAAI,WAAW,IAAI,SAAS,EAAE,CAE7B;IAED,IAAI,OAAO,IAAI,OAAO,CAMrB;IAED,WAAW,CAAC,KAAK,EAAE,QAAQ,GAAG,IAAI;IASlC,KAAK,IAAI,IAAI;IAWb,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,GAAE,CAAC,MAAM,IAAI,CAAC,GAAC,IAAW,GAAG,IAAI;IASxE,gBAAgB,IAAI,IAAI;IAYxB,oBAAoB,IAAI,MAAM;IAO9B,kBAAkB,CAAC,QAAQ,EAAE,QAAQ,GAAG,IAAI;IA4B5C,SAAS,CAAC,uBAAuB,IAAI,IAAI;IAIzC,SAAS,CAAC,sBAAsB,IAAI,IAAI;IAO1B,sBAAsB,IAAI,IAAI;IAQ5C,SAAS,CAAC,qBAAqB,IAAI,IAAI;IAqBvC,KAAK,IAAI,KAAK,EAAE;IAIhB,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI;IAchD,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI;IAcnD,KAAK,IAAI,MAAM;IAIf,SAAS,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI;IAOlD,eAAe,CAAC,QAAQ,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI;IAIlD,iBAAiB,CAAC,IAAI,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI;IAiBhD,UAAU,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,GAAG,IAAI;IAQzE,iBAAiB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC,QAAQ,EAAE,OAAO,KAAK,IAAI,GAAG,IAAI;IAIhF,SAAS,CAAC,mBAAmB,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,OAAO,GAAG,IAAI;IASxE,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,aAAa,KAAK,IAAI,EAAE,aAAa,EAAE,IAAI,GAAG,IAAI;IAC9E,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,oBAAoB,KAAK,IAAI,EAAE,aAAa,CAAC,EAAE,KAAK,GAAG,IAAI;IAQvF,gBAAgB,CAAC,QAAQ,EAAE,CAAC,CAAC,KAAK,EAAE,aAAa,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,oBAAoB,KAAK,IAAI,CAAC,GAAG,IAAI;IAI5G,kBAAkB,CAAC,KAAK,EAAE,aAAa,GAAG,IAAI;IAY9C,cAAc,CAAC,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE;QAAC,SAAS,CAAC,EAAE,MAAM,CAAC;QAAC,IAAI,CAAC,EAAE,oBAAoB,CAAA;KAAC,GAAG,IAAI;IAIxJ,MAAM,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,GAAG,QAAQ;IAQjH,MAAM,CAAC,4BAA4B,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,eAAe,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,GAAG,MAAM;IAqBxH,MAAM,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,OAAO;IAyC/G,MAAM,CAAC,2BAA2B,CAAC,eAAe,EAAE,MAAM,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI;IAgErI,MAAM,CAAC,2BAA2B,CAAC,WAAW,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,YAAY,GAAG,IAAI;IAgBvJ,MAAM,CAAC,uBAAuB,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,YAAY,EAAE;IAwC7H,MAAM,CAAC,yBAAyB,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,QAAQ,GAAG,KAAK;IA0D7E,cAAc,CAAC,gBAAgB,GAAE,OAAc,GAAG,MAAM;IAIxD,OAAO,CAAC,gBAAgB,GAAE,OAAc,GAAG,OAAO;IAalD,MAAM,CAAC,gBAAgB,CAAC,CAAC,EAAE,MAAM,GAAG,QAAQ;IAK5C,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,GAAG,QAAQ;IAuCzC,iBAAiB,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;IAKtC,kBAAkB;IAClB,wBAAwB,CAAC,MAAM,EAAE,oBAAoB,EAAE,GAAG,MAAM;IAIhE,iBAAiB,CAAC,MAAM,EAAE,oBAAoB,EAAE,GAAG,KAAK;IAsBxD,gBAAgB,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,GAAE,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,CAAa,EAAE,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI;CAqIxH"}