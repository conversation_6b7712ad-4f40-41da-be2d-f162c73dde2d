{"version": 3, "file": "file_input.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/models/widgets/file_input.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,CAAC,8BAAuB;AACpC,OAAO,EAAC,MAAM,EAAE,UAAU,EAAC,iBAA6B;AAExD,qBAAa,aAAc,SAAQ,UAAU;IAClC,KAAK,EAAE,SAAS,CAAA;IAEzB,SAAS,CAAC,SAAS,EAAE,gBAAgB,CAAA;IAE5B,eAAe,IAAI,IAAI;IAKvB,MAAM,IAAI,IAAI;IAsBjB,UAAU,CAAC,KAAK,EAAE,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC;IAoBhD,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,OAAO,CAAC,MAAM,CAAC;CAclD;AAED,yBAAiB,SAAS,CAAC;IACzB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG;QACjC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC,CAAA;QACpC,SAAS,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC,CAAA;QACxC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,MAAM,EAAE,CAAC,CAAA;QACvC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC1B,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;KAC9B,CAAA;CACF;AAED,MAAM,WAAW,SAAU,SAAQ,SAAS,CAAC,KAAK;CAAG;AAErD,qBAAa,SAAU,SAAQ,MAAM;IAC1B,UAAU,EAAE,SAAS,CAAC,KAAK,CAAA;IAC3B,aAAa,EAAE,aAAa,CAAA;gBAEzB,KAAK,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC;CAe7C"}