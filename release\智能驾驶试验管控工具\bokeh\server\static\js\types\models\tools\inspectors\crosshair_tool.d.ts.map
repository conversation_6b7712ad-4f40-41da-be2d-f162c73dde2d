{"version": 3, "file": "crosshair_tool.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/lib/models/tools/inspectors/crosshair_tool.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,WAAW,EAAE,eAAe,EAAC,MAAM,gBAAgB,CAAA;AAC3D,OAAO,EAAC,QAAQ,EAAC,MAAM,0BAA0B,CAAA;AACjD,OAAO,EAAC,IAAI,EAAC,MAAM,wBAAwB,CAAA;AAC3C,OAAO,EAAY,UAAU,EAAC,4BAAkB;AAChD,OAAO,EAAC,SAAS,EAAC,gCAAsB;AACxC,OAAO,KAAK,CAAC,iCAAuB;AACpC,OAAO,EAAC,KAAK,EAAC,4BAAkB;AAIhC,qBAAa,iBAAkB,SAAQ,eAAe;IAC3C,KAAK,EAAE,aAAa,CAAA;IAEpB,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,IAAI;IAY1B,UAAU,CAAC,EAAE,EAAE,SAAS,GAAG,IAAI;IAIxC,aAAa,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI;CAOxD;AAED,yBAAiB,aAAa,CAAC;IAC7B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG;QACtC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAClC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAA;QAC7B,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC9B,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAE9B,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC;YAAC,KAAK,EAAE,IAAI,CAAC;YAAC,MAAM,EAAE,IAAI,CAAA;SAAC,CAAC,CAAA;KAC/C,CAAA;CACF;AAED,MAAM,WAAW,aAAc,SAAQ,aAAa,CAAC,KAAK;CAAG;AAE7D,qBAAa,aAAc,SAAQ,WAAW;IACnC,UAAU,EAAE,aAAa,CAAC,KAAK,CAAA;IAC/B,aAAa,EAAE,iBAAiB,CAAA;gBAE7B,KAAK,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;IAuCvC,SAAS,SAAc;IACvB,IAAI,SAAsB;IAEnC,IAAa,OAAO,IAAI,MAAM,CAE7B;IAED,IAAa,mBAAmB,IAAI,QAAQ,EAAE,CAE7C;CACF"}