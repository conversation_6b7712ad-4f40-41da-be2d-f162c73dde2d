{"version": 3, "file": "ndarray.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/core/util/ndarray.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,QAAQ,EAAa,MAAM,UAAU,CAAA;AAG7C,OAAO,EAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAC,MAAM,MAAM,CAAA;AAClD,OAAO,EAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAC,MAAM,eAAe,CAAA;AAGjE,QAAA,MAAM,WAAW,eAAwB,CAAA;AAEzC,MAAM,WAAW,WAAY,SAAQ,SAAS,EAAE,YAAY;IAC1D,QAAQ,CAAC,CAAC,WAAW,CAAC,EAAE,OAAO,CAAA;IAC/B,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAA;IACxB,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAA;IACxB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAA;CAC3B;AAED,aAAK,OAAO,GAAG;IAAC,SAAS,EAAE,CAAC,CAAC;IAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAA;CAAC,CAAA;AAC9C,aAAK,OAAO,GAAG;IAAC,SAAS,EAAE,CAAC,CAAC;IAAC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;CAAC,CAAA;AAEtD,oBAAY,aAAa,GAAI,aAAa,GAAI,OAAO,CAAA;AACrD,oBAAY,YAAY,GAAK,YAAY,GAAK,OAAO,CAAA;AACrD,oBAAY,YAAY,GAAK,YAAY,GAAK,OAAO,CAAA;AACrD,oBAAY,cAAc,GAAG,cAAc,GAAG,OAAO,CAAA;AACrD,oBAAY,cAAc,GAAG,cAAc,GAAG,OAAO,CAAA;AACrD,oBAAY,YAAY,GAAK,cAAc,GAAG,cAAc,CAAA;AAE5D,qBAAa,YAAa,SAAQ,UAAW,YAAW,WAAW;IACjE,QAAQ,CAAC,CAAC,WAAW,CAAC,QAAO;IAC7B,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAU;IACjC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAA;IACxB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAA;gBAEd,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE;IAiBtE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO;IAI9C,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,UAAU,GAAG,OAAO;CAG9C;AAED,qBAAa,WAAY,SAAQ,SAAU,YAAW,WAAW;IAC/D,QAAQ,CAAC,CAAC,WAAW,CAAC,QAAO;IAC7B,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAS;IAC/B,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAA;IACxB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAA;gBAEd,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE;IAiBtE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO;IAI9C,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,UAAU,GAAG,OAAO;CAG9C;AAED,qBAAa,aAAc,SAAQ,WAAY,YAAW,WAAW;IACnE,QAAQ,CAAC,CAAC,WAAW,CAAC,QAAO;IAC7B,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAW;IACnC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAA;IACxB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAA;gBAEd,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE;IAiBtE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO;IAI9C,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,UAAU,GAAG,OAAO;CAG9C;AAED,qBAAa,YAAa,SAAQ,UAAW,YAAW,WAAW;IACjE,QAAQ,CAAC,CAAC,WAAW,CAAC,QAAO;IAC7B,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAU;IACjC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAA;IACxB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAA;gBAEd,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE;IAiBtE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO;IAI9C,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,UAAU,GAAG,OAAO;CAG9C;AAED,qBAAa,aAAc,SAAQ,WAAY,YAAW,WAAW;IACnE,QAAQ,CAAC,CAAC,WAAW,CAAC,QAAO;IAC7B,QAAQ,CAAC,KAAK,EAAE,QAAQ,CAAW;IACnC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAA;IACxB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAA;gBAEd,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE;IAiBtE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO;IAI9C,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,UAAU,GAAG,OAAO;CAG9C;AAED,qBAAa,YAAa,SAAQ,UAAW,YAAW,WAAW;IACjE,QAAQ,CAAC,CAAC,WAAW,CAAC,QAAO;IAC7B,QAAQ,CAAC,KAAK,EAAE,OAAO,CAAU;IACjC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAA;IACxB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAA;gBAEd,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE;IAiBtE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO;IAI9C,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,UAAU,GAAG,OAAO;CAG9C;AAED,qBAAa,cAAe,SAAQ,YAAa,YAAW,WAAW;IACrE,QAAQ,CAAC,CAAC,WAAW,CAAC,QAAO;IAC7B,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAY;IACrC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAA;IACxB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAA;gBAEd,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE;IAiBtE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO;IAI9C,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,UAAU,GAAG,OAAO;CAG9C;AAED,qBAAa,cAAe,SAAQ,YAAa,YAAW,WAAW;IACrE,QAAQ,CAAC,CAAC,WAAW,CAAC,QAAO;IAC7B,QAAQ,CAAC,KAAK,EAAE,SAAS,CAAY;IACrC,QAAQ,CAAC,KAAK,EAAE,MAAM,EAAE,CAAA;IACxB,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAA;gBAEd,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,eAAe,EAAE,KAAK,CAAC,EAAE,MAAM,EAAE;IAiBtE,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,UAAU,GAAG,OAAO;IAI9C,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,UAAU,GAAG,OAAO;CAG9C;AAED,oBAAY,OAAO,GACjB,YAAY,GAAK,WAAW,GAC5B,aAAa,GAAI,YAAY,GAC7B,aAAa,GAAI,YAAY,GAC7B,cAAc,GAAG,cAAc,CAAA;AAEjC,wBAAgB,UAAU,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,IAAI,OAAO,CAEnD;AAED,oBAAY,YAAY,GAAG;IACzB,OAAO,EAAI;QAAC,KAAK,EAAE,UAAU,CAAC;QAAG,OAAO,EAAE,YAAY,CAAA;KAAC,CAAA;IACvD,MAAM,EAAK;QAAC,KAAK,EAAE,SAAS,CAAC;QAAI,OAAO,EAAE,WAAW,CAAA;KAAC,CAAA;IACtD,QAAQ,EAAG;QAAC,KAAK,EAAE,WAAW,CAAC;QAAE,OAAO,EAAE,aAAa,CAAA;KAAC,CAAA;IACxD,OAAO,EAAI;QAAC,KAAK,EAAE,UAAU,CAAC;QAAG,OAAO,EAAE,YAAY,CAAA;KAAC,CAAA;IACvD,QAAQ,EAAG;QAAC,KAAK,EAAE,WAAW,CAAC;QAAE,OAAO,EAAE,aAAa,CAAA;KAAC,CAAA;IACxD,OAAO,EAAI;QAAC,KAAK,EAAE,UAAU,CAAC;QAAG,OAAO,EAAE,YAAY,CAAA;KAAC,CAAA;IACvD,SAAS,EAAE;QAAC,KAAK,EAAE,YAAY,CAAC;QAAC,OAAO,EAAE,cAAc,CAAA;KAAC,CAAA;IACzD,SAAS,EAAE;QAAC,KAAK,EAAE,YAAY,CAAC;QAAC,OAAO,EAAE,cAAc,CAAA;KAAC,CAAA;CAC1D,CAAA;AAED,wBAAgB,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,MAAM,EAAE,EAAE,OAAO,EAAE;IAAC,KAAK,EAAE,OAAO,CAAC;IAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAA;CAAC,GAAG,YAAY,GAAG,OAAO,CAAA;AAC1H,wBAAgB,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,MAAM,EAAE,EAAE,OAAO,EAAE;IAAC,KAAK,EAAE,OAAO,CAAC;IAAC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;CAAC,GAAG,YAAY,GAAG,OAAO,CAAA;AAClI,wBAAgB,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,MAAM,EAAE,EAAE,OAAO,EAAE;IAAC,KAAK,EAAE,QAAQ,CAAC;IAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAA;CAAC,GAAG,aAAa,GAAG,OAAO,CAAA;AAC5H,wBAAgB,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,MAAM,EAAE,EAAE,OAAO,EAAE;IAAC,KAAK,EAAE,QAAQ,CAAC;IAAC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;CAAC,GAAG,aAAa,GAAG,OAAO,CAAA;AACpI,wBAAgB,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,MAAM,EAAE,EAAE,OAAO,EAAE;IAAC,KAAK,EAAE,SAAS,CAAC;IAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAA;CAAC,GAAG,cAAc,GAAG,OAAO,CAAA;AAC9H,wBAAgB,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,MAAM,EAAE,EAAE,OAAO,EAAE;IAAC,KAAK,EAAE,SAAS,CAAC;IAAC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;CAAC,GAAG,cAAc,GAAG,OAAO,CAAA;AACtI,wBAAgB,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,MAAM,EAAE,EAAE,OAAO,EAAE;IAAC,KAAK,EAAE,SAAS,CAAC;IAAC,KAAK,EAAE,CAAC,MAAM,CAAC,CAAA;CAAC,GAAG,cAAc,GAAG,OAAO,CAAA;AAC9H,wBAAgB,OAAO,CAAC,KAAK,EAAE,WAAW,GAAG,MAAM,EAAE,EAAE,OAAO,EAAE;IAAC,KAAK,EAAE,SAAS,CAAC;IAAC,KAAK,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;CAAC,GAAG,cAAc,GAAG,OAAO,CAAA;AAEtI,wBAAgB,OAAO,CAAC,CAAC,SAAS,QAAQ,GAAG,SAAS,EAAE,KAAK,EAAE,WAAW,GAAG,MAAM,EAAE,EAAE,OAAO,CAAC,EAAE;IAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAA;CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA;AAC3J,wBAAgB,OAAO,CAAC,CAAC,SAAS,QAAQ,EAAE,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE;IAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,EAAE,CAAA;CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAA"}