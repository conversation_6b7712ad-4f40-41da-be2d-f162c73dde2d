# 智能驾驶试验管控系统 - 软件架构与技术文档

## 📋 文档概述

**项目名称**: 智能驾驶试验管控系统 (AVTG - Autonomous Vehicle Test Management System)
**版本**: 1.0
**文档类型**: 软件架构与技术文档
**编写日期**: 2025年8月3日
**适用对象**: 开发人员、系统架构师、技术维护人员

## 🎯 系统概述

智能驾驶试验管控系统是一个专业的试验数据管理和分析平台，主要用于智能驾驶汽车的试验过程管控、问题跟踪、数据分析和可视化展示。系统采用桌面应用架构，基于PyQt5框架开发，提供完整的试验数据生命周期管理功能。

### 核心功能
- **试验问题管理**: 记录、跟踪和分析试验过程中发现的问题
- **多表数据管理**: 支持原始记录、行驶记录、状态确认、费用记录等多种数据类型
- **数据可视化**: 提供丰富的图表和看板功能，支持数据分析和展示
- **项目管理**: 支持项目和文件的组织管理
- **数据导入导出**: 支持Excel文件的导入导出和数据同步
- **公式计算**: 支持列级别的公式计算功能

## 🏗️ 系统架构

### 整体架构设计

系统采用经典的MVC（Model-View-Controller）架构模式，结合模块化设计理念：

```
┌─────────────────────────────────────────────────────────────┐
│                    表示层 (Presentation Layer)                │
├─────────────────────────────────────────────────────────────┤
│  主窗口界面  │  对话框组件  │  表格组件  │  图表组件  │  工具栏  │
├─────────────────────────────────────────────────────────────┤
│                    业务逻辑层 (Business Logic Layer)           │
├─────────────────────────────────────────────────────────────┤
│  控制器模块  │  管理器模块  │  服务模块  │  工具模块  │  公式引擎 │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层 (Data Access Layer)             │
├─────────────────────────────────────────────────────────────┤
│  数据模型    │  数据库操作  │  配置管理  │  文件处理  │  缓存管理 │
├─────────────────────────────────────────────────────────────┤
│                    基础设施层 (Infrastructure Layer)          │
└─────────────────────────────────────────────────────────────┘
│  SQLite数据库 │  配置文件   │  资源文件  │  日志系统  │  Python环境│
```

### 目录结构

```
AVTG0803-1/
├── run.py                          # 主启动脚本
├── fast_run.py                     # 快速启动脚本
├── offline_launcher.bat            # 离线启动脚本
├── 智能驾驶试验管控数据库.db        # 主数据库文件
├── admin_config.json               # 管理员配置
├── src/                            # 源代码目录
│   ├── controllers/                # 控制器模块
│   │   └── main_controller.py      # 主控制器
│   ├── models/                     # 数据模型
│   │   ├── problem.py              # 问题表模型
│   │   ├── original.py             # 原始记录表模型
│   │   ├── drive.py                # 行驶记录表模型
│   │   ├── status.py               # 状态确认表模型
│   │   ├── change.py               # 零部件换装表模型
│   │   ├── log.py                  # 试验日志表模型
│   │   ├── fees.py                 # 费用记录表模型
│   │   ├── visualization.py        # 可视化数据模型
│   │   └── database_merger.py      # 数据库合并器
│   ├── views/                      # 视图组件
│   │   ├── main_window.py          # 主窗口
│   │   ├── *_table_widget.py       # 各种表格组件
│   │   ├── data_visualization.py   # 数据可视化窗口
│   │   ├── chart_creator.py        # 图表创建器
│   │   ├── dashboard_editor.py     # 看板编辑器
│   │   └── dashboard_viewer.py     # 看板查看器
│   ├── managers/                   # 管理器模块
│   │   ├── *_table_manager.py      # 各表格管理器
│   │   └── project_manager.py      # 项目管理器
│   ├── services/                   # 服务模块
│   │   ├── database_init_service.py # 数据库初始化服务
│   │   └── transfer_sync_service.py # 数据传递同步服务
│   ├── dialogs/                    # 对话框组件
│   ├── formula/                    # 公式计算引擎
│   ├── utils/                      # 工具模块
│   ├── widgets/                    # 自定义控件
│   └── resources/                  # 资源文件
├── config/                         # 配置文件目录
├── app/                           # 应用程序数据
├── python/                        # 嵌入式Python环境
└── temp/                          # 临时文件和文档
```

## 🔧 技术栈

### 核心技术框架

| 技术组件 | 版本要求 | 用途说明 |
|---------|---------|---------|
| **Python** | 3.8+ | 主要开发语言 |
| **PyQt5** | >=5.15.0 | GUI框架，提供桌面应用界面 |
| **PyQtWebEngine** | >=5.15.0 | Web引擎，用于图表渲染 |
| **SQLite** | 内置 | 嵌入式数据库，存储业务数据 |

### 数据处理与分析

| 技术组件 | 版本要求 | 用途说明 |
|---------|---------|---------|
| **pandas** | >=1.3.0 | 数据处理和分析 |
| **numpy** | >=1.20.0 | 数值计算和数组操作 |
| **openpyxl** | >=3.1.0 | Excel文件读写 |
| **xlrd** | >=2.0.1 | Excel文件读取 |

### 数据可视化

| 技术组件 | 版本要求 | 用途说明 |
|---------|---------|---------|
| **pyecharts** | >=2.0.0 | 交互式图表生成 |
| **matplotlib** | >=3.4.2 | 基础图表绘制 |
| **pyqtgraph** | >=0.12.3 | 实时图表显示 |

### 文档与报告

| 技术组件 | 版本要求 | 用途说明 |
|---------|---------|---------|
| **reportlab** | >=4.0.0 | PDF报告生成 |
| **Pillow** | >=8.3.1 | 图像处理 |
| **weasyprint** | >=60.0 | HTML到PDF转换 |

## 💾 数据库设计

### 数据库架构

系统使用SQLite作为主要数据存储，数据库文件：`智能驾驶试验管控数据库.db`

### 主要数据表

#### 1. 试验问题表 (Problem)
```sql
CREATE TABLE "Problem" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "试验类型" TEXT,
    "样车编号" TEXT,
    "问题编号_QTM" TEXT,
    "问题编号_DTX" TEXT,
    "问题描述_QTM_DTS_DTM" TEXT,
    "图片名称" TEXT,
    "图片路径" TEXT,
    "图片链接" TEXT,
    "纬度" TEXT,
    "经度" TEXT,
    "测试软件版本" TEXT,
    "问题日期_QTM_DTS_DTM" TEXT,
    "提出人" TEXT,
    "提出人员工编号_QTM_DTM" TEXT,
    "合并问题单号" TEXT,
    "修改时间" TEXT,
    "问题表固定字段" TEXT,  -- JSON格式存储
    "问题表可变字段" TEXT   -- JSON格式存储
)
```

#### 2. 原始记录表 (Original)
```sql
CREATE TABLE "Original" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "样车编号" TEXT,
    "问题简述" TEXT,
    "问题描述_QTM_DTS_DTM" TEXT,
    "纬度" TEXT,
    "经度" TEXT,
    "测试软件版本" TEXT,
    "问题日期_QTM_DTS_DTM" TEXT,
    "具体时间" TEXT,
    "问题提出人" TEXT,
    "问题有效性" TEXT,
    "原始表固定字段" TEXT,  -- JSON格式存储
    "原始表可变字段" TEXT,  -- JSON格式存储
    "修改时间" TEXT
)
```

#### 3. 行驶记录表 (Drive)
```sql
CREATE TABLE "Drive" (
    "id" INTEGER PRIMARY KEY AUTOINCREMENT,
    "日期" TEXT,
    "样车编号" TEXT,
    "天气" TEXT,
    "试验地区" TEXT,
    "试验工况" TEXT,
    "试验路线" TEXT,
    "试验里程" REAL,
    "泊车次数" INTEGER,
    "加油量" REAL,
    "充电量" REAL,
    "驾驶员" TEXT,
    "驾驶员工时" REAL,
    "测试员" TEXT,
    "测试员工时" REAL,
    "行驶记录表固定字段" TEXT,  -- JSON格式存储
    "行驶记录表可变字段" TEXT,  -- JSON格式存储
    "修改时间" TEXT
)
```

#### 4. 其他业务表
- **试验状态确认表 (status)**: 存储试验状态信息
- **零部件换装表 (change)**: 存储零部件更换记录
- **试验日志表 (log)**: 存储试验日志信息
- **费用记录表 (fees)**: 存储费用相关数据

#### 5. 可视化相关表
- **charts**: 图表配置信息
- **dashboards**: 看板配置信息
- **data_sources**: 数据源配置
- **chart_templates**: 图表模板

### 数据存储策略

1. **混合存储模式**:
   - 常用字段独立存储，便于查询和索引
   - 扩展字段使用JSON格式存储，提供灵活性

2. **ID自增机制**:
   - 所有主表使用`INTEGER PRIMARY KEY AUTOINCREMENT`
   - 确保数据唯一性和一致性

3. **时间戳管理**:
   - 每条记录包含`修改时间`字段
   - 支持数据变更追踪

## 🔄 核心业务流程

### 1. 数据录入流程
```
用户操作 → 表格编辑 → 数据验证 → 模型更新 → 数据库保存 → 界面刷新
```

### 2. 数据导入流程
```
Excel文件 → 字段映射 → 数据转换 → 批量导入 → 结果反馈
```

### 3. 数据可视化流程
```
数据查询 → 图表配置 → 图表生成 → 看板组装 → 交互展示
```

### 4. 项目管理流程
```
项目创建 → 文件关联 → 数据管理 → 项目保存 → 状态恢复
```

## 📊 关键模块说明

### 1. 主控制器 (MainController)
- **职责**: 协调各个模块，处理用户交互
- **核心功能**: 项目管理、文件操作、数据同步
- **关键方法**: `new_project()`, `open_file()`, `save_project()`

### 2. 表格管理器 (TableManager)
- **职责**: 管理各种数据表的CRUD操作
- **核心功能**: 数据加载、编辑、保存、删除
- **支持表格**: 问题表、原始记录表、行驶记录表等

### 3. 数据可视化模块
- **图表创建器**: 支持多种图表类型创建
- **看板编辑器**: 拖拽式看板设计
- **看板查看器**: 交互式数据展示

### 4. 公式计算引擎
- **列级公式**: 支持整列的公式计算
- **函数库**: 数值计算、条件判断、字符串处理
- **依赖管理**: 自动处理列间依赖关系

### 5. 配置管理系统
- **字段配置**: 动态字段定义和显示控制
- **界面状态**: 窗口布局和用户偏好保存
- **权限管理**: 字段编辑权限控制

## 🔧 技术特性

### 1. 模块化设计
- 清晰的模块边界和职责分离
- 松耦合的组件设计
- 易于扩展和维护

### 2. 配置驱动
- 字段定义通过JSON配置文件管理
- 界面布局和行为可配置
- 支持运行时配置更新

### 3. 多线程支持
- 异步数据加载和处理
- 非阻塞的用户界面
- 后台任务处理

### 4. 错误处理
- 完善的异常处理机制
- 详细的日志记录
- 用户友好的错误提示

### 5. 性能优化
- 数据分页加载
- 缓存机制
- 向量化计算

## 📝 开发规范

### 1. 代码组织
- 遵循PEP 8编码规范
- 使用中文注释和文档字符串
- 模块化的文件组织结构

### 2. 数据库操作
- 使用参数化查询防止SQL注入
- 事务管理确保数据一致性
- 连接池管理提高性能

### 3. 界面设计
- 响应式布局设计
- 一致的用户体验
- 无障碍访问支持

### 4. 测试策略
- 单元测试覆盖核心功能
- 集成测试验证模块协作
- 用户验收测试确保需求满足

## 🚀 部署与维护

### 1. 部署要求
- Windows 10/11操作系统
- 4GB以上内存
- 2GB可用磁盘空间
- 嵌入式Python环境

### 2. 启动方式
- **标准启动**: `python run.py`
- **快速启动**: `python fast_run.py`
- **离线启动**: `offline_launcher.bat`

### 3. 维护要点
- 定期备份数据库文件
- 监控日志文件大小
- 更新配置文件
- 清理临时文件

---

**文档版本**: 1.0
**最后更新**: 2025年8月3日
**维护团队**: AVTG开发团队