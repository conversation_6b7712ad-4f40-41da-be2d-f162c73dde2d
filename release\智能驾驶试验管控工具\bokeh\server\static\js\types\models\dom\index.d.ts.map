{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/models/dom/index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,KAAK,EAAC,MAAM,aAAa,CAAA;AACjC,OAAO,EAAC,SAAS,EAAE,aAAa,EAAC,MAAM,uBAAuB,CAAA;AAC9D,OAAO,EAAC,MAAM,EAAC,MAAM,UAAU,CAAA;AAE/B,OAAO,EAAC,IAAI,EAAC,wBAAiB;AAC9B,OAAO,EAAC,OAAO,EAAC,4BAAqB;AAErC,OAAO,KAAK,CAAC,8BAAuB;AAKpC,OAAO,EAAC,KAAK,IAAI,SAAS,EAAoB,mCAA4B;AAC1E,OAAO,EAAC,kBAAkB,EAAC,MAAM,iCAAiC,CAAA;AAElE,OAAO,EAAC,MAAM,EAAC,CAAA;AAEf,8BAAsB,WAAY,SAAQ,OAAO;IACtC,KAAK,EAAE,OAAO,CAAA;CACxB;AAED,yBAAiB,OAAO,CAAC;IACvB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;CAChC;AAED,MAAM,WAAW,OAAQ,SAAQ,OAAO,CAAC,KAAK;CAAG;AAEjD,8BAAsB,OAAQ,SAAQ,KAAK;IAChC,UAAU,EAAE,OAAO,CAAC,KAAK,CAAA;IACzB,aAAa,EAAE,WAAW,CAAA;IACnC,OAAgB,UAAU,SAAqB;gBAEnC,KAAK,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC;CAG3C;AAED,qBAAa,QAAS,SAAQ,WAAW;IAC9B,KAAK,EAAE,IAAI,CAAA;IACX,EAAE,EAAE,UAAU,CAAC,IAAI,CAAA;IAEnB,MAAM,IAAI,IAAI;cAKJ,cAAc,IAAI,UAAU,CAAC,IAAI;CAGrD;AAED,yBAAiB,IAAI,CAAC;IACpB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG;QAClC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;KAC5B,CAAA;CACF;AAED,MAAM,WAAW,IAAK,SAAQ,IAAI,CAAC,KAAK;CAAG;AAE3C,qBAAa,IAAK,SAAQ,OAAO;IACtB,UAAU,EAAE,IAAI,CAAC,KAAK,CAAA;IACtB,aAAa,EAAE,QAAQ,CAAA;gBAEpB,KAAK,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;CAUxC;AAED,8BAAsB,eAAgB,SAAQ,WAAW;IAC9C,KAAK,EAAE,WAAW,CAAA;IAC3B,OAAgB,QAAQ,SAAkB;IAE1C,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,kBAAkB,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,GAAgC,IAAI;CAC3G;AAED,yBAAiB,WAAW,CAAC;IAC3B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,EAAE,CAAA;CACvC;AAED,MAAM,WAAW,WAAY,SAAQ,WAAW,CAAC,KAAK;CAAG;AAEzD,8BAAsB,WAAY,SAAQ,OAAO;IACtC,UAAU,EAAE,WAAW,CAAC,KAAK,CAAA;IAC7B,aAAa,EAAE,eAAe,CAAA;gBAE3B,KAAK,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC;CAO/C;AAED,qBAAa,SAAU,SAAQ,eAAe;IACnC,KAAK,EAAE,KAAK,CAAA;IAErB,MAAM,CAAC,OAAO,EAAE,kBAAkB,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,GAAgC,IAAI;CAGpG;AAED,yBAAiB,KAAK,CAAC;IACrB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG,EAAE,CAAA;CAC3C;AAED,MAAM,WAAW,KAAM,SAAQ,KAAK,CAAC,KAAK;CAAG;AAE7C,qBAAa,KAAM,SAAQ,WAAW;IAC3B,UAAU,EAAE,KAAK,CAAC,KAAK,CAAA;IACvB,aAAa,EAAE,SAAS,CAAA;gBAErB,KAAK,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;CAQzC;AAED,qBAAa,YAAa,SAAQ,eAAe;IACtC,KAAK,EAAE,QAAQ,CAAA;IAExB,MAAM,CAAC,MAAM,EAAE,kBAAkB,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,GAAgC,IAAI;CAKnG;AAED,yBAAiB,QAAQ,CAAC;IACxB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG;QACtC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;KAC1B,CAAA;CACF;AAED,MAAM,WAAW,QAAS,SAAQ,QAAQ,CAAC,KAAK;CAAG;AAEnD,qBAAa,QAAS,SAAQ,WAAW;IAC9B,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAA;IAC1B,aAAa,EAAE,YAAY,CAAA;gBAExB,KAAK,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;CAU5C;AAED,qBAAa,YAAa,SAAQ,YAAY;IACnC,KAAK,EAAE,QAAQ,CAAA;IAExB,QAAQ,CAAC,EAAE,WAAW,CAAA;IACtB,SAAS,CAAC,EAAE,WAAW,CAAA;IAEd,MAAM,IAAI,IAAI;IAUd,MAAM,CAAC,MAAM,EAAE,kBAAkB,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,GAAgC,IAAI;CAK5G;AAED,yBAAiB,QAAQ,CAAC;IACxB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG;QACnC,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QACxB,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;KAC5B,CAAA;CACF;AAED,MAAM,WAAW,QAAS,SAAQ,QAAQ,CAAC,KAAK;CAAG;AAEnD,qBAAa,QAAS,SAAQ,QAAQ;IAC3B,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAA;IAC1B,aAAa,EAAE,YAAY,CAAA;gBAExB,KAAK,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;CAW5C;AAED,8BAAsB,cAAe,SAAQ,WAAW;IAC7C,KAAK,EAAE,UAAU,CAAA;IACjB,EAAE,EAAE,WAAW,CAAA;IAExB,WAAW,EAAE,GAAG,CAAC,OAAO,GAAG,SAAS,EAAE,WAAW,GAAG,aAAa,CAAC,CAAY;IAE/D,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC;IAMtC,MAAM,IAAI,IAAI;CA0CxB;AAED,yBAAiB,UAAU,CAAC;IAC1B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG;QAClC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG;YAAC,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;SAAC,GAAG,IAAI,CAAC,CAAA;QAC1D,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,GAAG,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC,CAAA;KACvD,CAAA;CACF;AAED,MAAM,WAAW,UAAW,SAAQ,UAAU,CAAC,KAAK;CAAG;AAEvD,8BAAsB,UAAW,SAAQ,OAAO;IACrC,UAAU,EAAE,UAAU,CAAC,KAAK,CAAA;IAC5B,aAAa,EAAE,cAAc,CAAA;gBAE1B,KAAK,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC;CAU9C;AAED,8BAAsB,UAAW,SAAQ,IAAI;IAClC,KAAK,EAAE,MAAM,CAAA;IAEtB,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,kBAAkB,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,GAAgC,IAAI;CAC3G;AAED,yBAAiB,MAAM,CAAC;IACtB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAA;CACrC;AAED,MAAM,WAAW,MAAO,SAAQ,MAAM,CAAC,KAAK;CAAG;AAE/C,8BAAsB,MAAO,SAAQ,KAAK;IAC/B,UAAU,EAAE,MAAM,CAAC,KAAK,CAAA;IACxB,aAAa,EAAE,UAAU,CAAA;IAClC,OAAgB,UAAU,SAAqB;gBAEnC,KAAK,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;CAO1C;AAED,qBAAa,YAAa,SAAQ,cAAc;IACrC,KAAK,EAAE,QAAQ,CAAA;IACxB,OAAgB,QAAQ,QAAiB;IAEzC,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,CAAY;IAElC,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC;IAKtC,MAAM,IAAI,IAAI;IAKvB,MAAM,CAAC,MAAM,EAAE,kBAAkB,EAAE,CAAC,EAAE,SAAS,EAAE,IAAI,GAAE,MAAW,GAAgC,IAAI;CAiBvG;AAED,yBAAiB,QAAQ,CAAC;IACxB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG;QACrC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;KAC9B,CAAA;CACF;AAED,MAAM,WAAW,QAAS,SAAQ,QAAQ,CAAC,KAAK;CAAG;AAEnD,qBAAa,QAAS,SAAQ,UAAU;IAC7B,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAA;IAC1B,aAAa,EAAE,YAAY,CAAA;CAQrC;AAED,qBAAa,QAAS,SAAQ,cAAc;IACjC,KAAK,EAAE,IAAI,CAAA;IACpB,OAAgB,QAAQ,SAAkB;CAC3C;AACD,qBAAa,IAAK,SAAQ,UAAU;IACzB,aAAa,EAAE,QAAQ,CAAA;CAIjC;AAED,qBAAa,OAAQ,SAAQ,cAAc;IAChC,KAAK,EAAE,GAAG,CAAA;IACnB,OAAgB,QAAQ,QAAiB;CAC1C;AACD,qBAAa,GAAI,SAAQ,UAAU;IACxB,aAAa,EAAE,OAAO,CAAA;CAIhC;AAED,qBAAa,SAAU,SAAQ,cAAc;IAClC,KAAK,EAAE,KAAK,CAAA;IACrB,OAAgB,QAAQ,UAAmB;CAC5C;AACD,qBAAa,KAAM,SAAQ,UAAU;IAC1B,aAAa,EAAE,SAAS,CAAA;CAIlC;AAED,qBAAa,YAAa,SAAQ,cAAc;IACrC,KAAK,EAAE,QAAQ,CAAA;IACxB,OAAgB,QAAQ,OAAgB;CACzC;AACD,qBAAa,QAAS,SAAQ,UAAU;IAC7B,aAAa,EAAE,YAAY,CAAA;CAIrC;AAID,OAAO,EAAC,aAAa,EAAC,MAAM,uBAAuB,CAAA;AAGnD,qBAAa,eAAgB,SAAQ,UAAU;IACpC,KAAK,EAAE,WAAW,CAAA;IAE3B,MAAM,CAAC,OAAO,EAAE,kBAAkB,EAAE,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,GAAgC,IAAI;CAKpG;AAED,yBAAiB,WAAW,CAAC;IAC3B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG;QACjC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,aAAa,EAAE,CAAC,CAAA;KACpC,CAAA;CACF;AAED,MAAM,WAAW,WAAY,SAAQ,WAAW,CAAC,KAAK;CAAG;AAEzD,qBAAa,WAAY,SAAQ,MAAM;IAC5B,UAAU,EAAE,WAAW,CAAC,KAAK,CAAA;IAC7B,aAAa,EAAE,eAAe,CAAA;gBAE3B,KAAK,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC;CAU/C"}