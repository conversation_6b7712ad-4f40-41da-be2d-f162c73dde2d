{"version": 3, "file": "svg.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/core/util/svg.ts"], "names": [], "mappings": "AAAA;;GAEG;AAEH,OAAO,EAAC,eAAe,EAAC,MAAM,UAAU,CAAA;AAExC,OAAO,EAAS,MAAM,EAAC,MAAM,UAAU,CAAA;AAGvC,aAAK,EAAE,CAAC,CAAC,IAAI;IAAC,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,CAAA;CAAC,CAAA;AAE/B,aAAK,QAAQ,GAAG;IACd,KAAK,EAAE,MAAM,CAAA;IACb,IAAI,EAAE,MAAM,CAAA;IACZ,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,EAAE,MAAM,CAAA;CACnB,CAAA;AA6DD,aAAK,KAAK,GAAG;IACX,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,MAAM,EAAE,OAAO,CAAA;IACf,GAAG,CAAC,EAAE,OAAO,CAAA;IACb,KAAK,CAAC,EAAE,MAAM,CAAA;CACf,CAAA;AAED,aAAK,SAAS,GACZ,aAAa,GACb,WAAW,GACX,SAAS,GACT,UAAU,GACV,YAAY,GACZ,WAAW,GACX,aAAa,GACb,MAAM,GACN,aAAa,GACb,eAAe,GACf,eAAe,GACf,YAAY,GACZ,WAAW,GACX,cAAc,GACd,UAAU,GACV,gBAAgB,CAAA;AAElB,aAAK,UAAU,GAAG;KAAE,GAAG,IAAI,SAAS,GAAG,KAAK;CAAC,CAAA;AAkF7C,cAAM,cAAe,YAAW,UAAU,CAAC,cAAc;IACvD,MAAM,EAAE,UAAU,CAAA;IAClB,KAAK,EAAE,qBAAqB,CAAA;gBAEhB,YAAY,EAAE,UAAU,EAAE,GAAG,EAAE,qBAAqB;IAKhE;;OAEG;IACH,YAAY,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;CA4BlD;AAED,cAAM,aAAc,YAAW,UAAU,CAAC,aAAa;IACrD,MAAM,EAAE,iBAAiB,CAAA;IACzB,KAAK,EAAE,qBAAqB,CAAA;gBAEhB,OAAO,EAAE,iBAAiB,EAAE,GAAG,EAAE,qBAAqB;IAKlE,YAAY,CAAC,UAAU,CAAC,EAAE,eAAe,GAAG,IAAI;CAGjD;AAED,aAAK,OAAO,GAAG;IACb,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,QAAQ,CAAC,EAAE,QAAQ,CAAA;IACnB,GAAG,CAAC,EAAE,wBAAwB,CAAA;CAC/B,CAAA;AAED,aAAK,IAAI,GAAG,MAAM,CAAA;AAElB,aAAK,cAAc,GAAG;IACpB,SAAS,EAAE,eAAe,CAAA;IAC1B,SAAS,EAAE,IAAI,GAAG,IAAI,CAAA;IACtB,UAAU,EAAE,UAAU,CAAA;CACvB,CAAA;AAED;;;;;;;GAOG;AAEH,aAAK,4BAA4B,GAC/B,iBAAiB,GACjB,eAAe,GACf,cAAc,GACd,sBAAsB,GACtB,aAAa,GACb,eAAe,GACf,oBAAoB,GACpB,UAAU,GACV,uBAAuB,GACvB,UAAU,GACV,kBAAkB,GAClB,WAAW,GACX,UAAU,GACV,uBAAuB,GACvB,eAAe,GACf,mBAAmB,CAAA;AAErB,qBAAa,qBAAsB,YAAW,4BAA4B;IACxE,QAAQ,EAAE,iBAAiB,CAAA;IAC3B,KAAK,EAAE,wBAAwB,CAAA;IAE/B,MAAM,EAAE,aAAa,CAAA;IACrB,KAAK,EAAE,GAAG,CAAC,MAAM,CAAC,CAAA;IAClB,MAAM,EAAE,UAAU,CAAA;IAClB,OAAO,EAAE,cAAc,EAAE,CAAA;IACzB,UAAU,EAAE,QAAQ,CAAA;IACpB,gBAAgB,EAAE,UAAU,CAAA;IAC5B,oBAAoB,EAAE,MAAM,CAAA;IAC5B,iBAAiB,EAAE;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAC,GAAG,IAAI,CAAO;IAGvD,MAAM,CAAC,QAAQ,EAAE,MAAM,CAAS;IAEhC,IAAI,MAAM,IAAI,qBAAqB,CAGlC;IAED,WAAW,EAAE,MAAM,GAAG,cAAc,GAAG,aAAa,CAAA;IACpD,SAAS,EAAE,MAAM,GAAG,cAAc,GAAG,aAAa,CAAA;IAClD,OAAO,EAAE,aAAa,CAAA;IACtB,QAAQ,EAAE,cAAc,CAAA;IACxB,UAAU,EAAE,MAAM,CAAA;IAClB,SAAS,EAAE,MAAM,CAAA;IACjB,WAAW,EAAE,MAAM,CAAA;IACnB,wBAAwB,EAAE,MAAM,CAAA;IAChC,IAAI,EAAE,MAAM,CAAA;IACZ,SAAS,EAAE,eAAe,CAAA;IAC1B,WAAW,EAAE,MAAM,CAAA;IACnB,aAAa,EAAE,MAAM,CAAA;IACrB,aAAa,EAAE,MAAM,CAAA;IACrB,UAAU,EAAE,MAAM,CAAA;IAClB,SAAS,EAAE,eAAe,CAAA;IAC1B,YAAY,EAAE,kBAAkB,CAAA;IAChC,QAAQ,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,IAAI,CAAA;IAClC,cAAc,EAAE,MAAM,CAAA;IACtB,MAAM,EAAE,MAAM,CAAA;IACd,qBAAqB,EAAE,OAAO,CAAA;IAC9B,qBAAqB,EAAE,qBAAqB,CAAA;IAE5C,OAAO,CAAC,MAAM,CAAQ;IACtB,OAAO,CAAC,OAAO,CAAQ;IAEvB,IAAI,KAAK,IAAI,MAAM,CAElB;IACD,IAAI,KAAK,CAAC,KAAK,EAAE,MAAM,EAGtB;IACD,IAAI,MAAM,IAAI,MAAM,CAEnB;IACD,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,EAGxB;IAED,OAAO,CAAC,UAAU,CAAwB;gBAE9B,OAAO,CAAC,EAAE,OAAO;IAgC7B,SAAS,CAAC,cAAc,IAAI,MAAM;IASlC;;OAEG;IACH,eAAe,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,GAAE,EAAE,CAAC,MAAM,GAAG,MAAM,CAAM,EAAE,SAAS,GAAE,OAAe,GAAG,UAAU;IAclH;;OAEG;IACH,kBAAkB,IAAI,IAAI;IAU1B;;OAEG;IACH,iBAAiB,CAAC,UAAU,EAAE,UAAU,GAAG,IAAI;IAS/C;;OAEG;IACH,eAAe,IAAI,UAAU;IAU7B;;OAEG;IACH,4BAA4B,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;IA8DhD;;;;;QAKI;IACJ,kBAAkB,CAAC,gBAAgB,GAAE,OAAe,GAAG,MAAM;IAgB7D,OAAO,IAAI,aAAa;IAIxB;;QAEI;IACJ,IAAI,IAAI,IAAI;IAUZ;;QAEI;IACJ,OAAO,IAAI,IAAI;IAWf,OAAO,CAAC,gBAAgB;IAMxB;;QAEI;IACJ,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI;IAMlC;;QAEI;IACJ,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAM3B;;QAEI;IACJ,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAMrC;;QAEI;IACJ,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAMjF;;QAEI;IACJ,SAAS,IAAI,IAAI;IAQjB,SAAS,CAAC,cAAc,IAAI,IAAI;IAMhC;;QAEI;IACJ,yBAAyB,IAAI,IAAI;IASjC;;QAEI;IACJ,gBAAgB,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI;IAM1D,IAAI,sBAAsB,IAAI,OAAO,CAEpC;IAED;;;QAGI;IACJ,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAclC;;QAEI;IACJ,SAAS,IAAI,IAAI;IAMjB;;QAEI;IACJ,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAWlC;;QAEI;IACJ,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IASjG;;QAEI;IACJ,gBAAgB,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAQtE;;;;QAII;IACJ,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IA6F3E;;QAEI;IACJ,MAAM,IAAI,IAAI;IAWd;;QAEI;IACJ,IAAI,CAAC,SAAS,CAAC,EAAE,cAAc,GAAG,IAAI;IACtC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,cAAc,GAAG,IAAI;IA+BpD;;QAEI;IACJ,IAAI,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAU/D;;QAEI;IACJ,QAAQ,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAQnE;;;;;;QAMI;IACJ,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAQrE;;;;QAII;IACJ,aAAa,IAAI,IAAI;IAOrB;;QAEI;IACJ,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAYpE;;;QAGI;IACJ,oBAAoB,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,cAAc;IAiBpF;;;QAGI;IACJ,oBAAoB,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,cAAc;IAmB5G;;QAEI;IACJ,WAAW,IAAI,QAAQ;IAcvB;;QAEI;IACJ,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI;IAgChF;;QAEI;IACJ,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAMlD;;QAEI;IACJ,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAMpD;;QAEI;IACJ,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,WAAW;IAKtC,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,gBAAgB,GAAE,OAAe,GAAG,IAAI;IAI1H,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,gBAAgB,GAAE,OAAe,GAAG,IAAI;IA4CpK,OAAO,CAAC,UAAU,CAAoB;IAEtC;;QAEI;IACJ,IAAI,IAAI,IAAI;IAYZ,SAAS,CAAC,KAAK,EAAE,iBAAiB,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,IAAI;IACjE,SAAS,CAAC,KAAK,EAAE,iBAAiB,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,IAAI;IACzF,SAAS,CAAC,KAAK,EAAE,iBAAiB,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,IAAI;IAiIzI;;QAEI;IACJ,aAAa,CAAC,KAAK,EAAE,iBAAiB,EAAE,WAAW,EAAE,MAAM,GAAG,IAAI,GAAG,aAAa,GAAG,IAAI;IAmCzF,WAAW,IAAI,MAAM,EAAE;IAUvB,WAAW,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,IAAI;IAOrC,OAAO,CAAC,UAAU;IAIlB,YAAY,IAAI,SAAS;IAIzB,YAAY,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IACpF,YAAY,CAAC,SAAS,CAAC,EAAE,eAAe,GAAG,IAAI;IAC/C,YAAY,CAAC,MAAM,EAAE,SAAS,GAAG,IAAI;IAerC,cAAc,IAAI,IAAI;IAItB,aAAa,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,cAAc,GAAG,OAAO;IACxE,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,SAAS,CAAC,EAAE,cAAc,GAAG,OAAO;IAMtF,eAAe,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,OAAO;IAC9C,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,OAAO;IAM5D,eAAe,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,SAAS;IAClD,eAAe,CAAC,SAAS,EAAE,SAAS,GAAG,SAAS;IAMhD,YAAY,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,SAAS;IAI3E,YAAY,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,IAAI;IAChE,YAAY,CAAC,SAAS,EAAE,SAAS,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,IAAI;IAMzI,iBAAiB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;IACzC,iBAAiB,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,GAAG,IAAI;IAMvD,kBAAkB,IAAI,IAAI;IAC1B,kBAAkB,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;CAKvC"}