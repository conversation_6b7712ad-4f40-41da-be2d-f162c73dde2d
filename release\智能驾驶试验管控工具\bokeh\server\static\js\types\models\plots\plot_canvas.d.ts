import { Cartesian<PERSON>rame } from "../canvas/cartesian_frame";
import { CanvasView, FrameBox } from "../canvas/canvas";
import { Renderer, RendererView } from "../renderers/renderer";
import { DataRenderer } from "../renderers/data_renderer";
import { Tool, ToolView } from "../tools/tool";
import { Selection } from "../selections/selection";
import { LayoutDOM, LayoutDOMView } from "../layouts/layout_dom";
import { Plot } from "./plot";
import { Title } from "../annotations/title";
import { AxisView } from "../axes/axis";
import { ToolbarPanel } from "../annotations/toolbar_panel";
import { Renderable } from "../../core/visuals";
import { RenderLevel } from "../../core/enums";
import { SerializableState } from "../../core/view";
import { Context2d, CanvasLayer } from "../../core/util/canvas";
import { SizingPolicy, Layoutable } from "../../core/layout";
import { BorderLayout } from "../../core/layout/border";
import { BBox } from "../../core/util/bbox";
import { RangeInfo, RangeOptions, RangeManager } from "./range_manager";
import { StateInfo, StateManager } from "./state_manager";
export declare class PlotView extends LayoutDOMView implements Renderable {
    model: Plot;
    visuals: Plot.Visuals;
    layout: BorderLayout;
    frame: CartesianFrame;
    canvas_view: CanvasView;
    get canvas(): CanvasView;
    protected _title: Title;
    protected _toolbar: ToolbarPanel;
    protected _outer_bbox: BBox;
    protected _inner_bbox: BBox;
    protected _needs_paint: boolean;
    protected _needs_layout: boolean;
    protected _invalidated_painters: Set<RendererView>;
    protected _invalidate_all: boolean;
    protected _state_manager: StateManager;
    protected _range_manager: RangeManager;
    get state(): StateManager;
    set invalidate_dataranges(value: boolean);
    visibility_callbacks: ((visible: boolean) => void)[];
    protected _is_paused?: number;
    protected lod_started: boolean;
    protected _initial_state: StateInfo;
    protected throttled_paint: () => void;
    computed_renderers: Renderer[];
    renderer_view<T extends Renderer>(renderer: T): T["__view_type__"] | undefined;
    renderer_views: Map<Renderer, RendererView>;
    tool_views: Map<Tool, ToolView>;
    get is_paused(): boolean;
    get child_models(): LayoutDOM[];
    pause(): void;
    unpause(no_render?: boolean): void;
    private _needs_notify;
    notify_finished_after_paint(): void;
    request_render(): void;
    request_paint(to_invalidate: RendererView[] | RendererView | "everything"): void;
    invalidate_painters(to_invalidate: RendererView[] | RendererView | "everything"): void;
    schedule_paint(): void;
    request_layout(): void;
    reset(): void;
    remove(): void;
    render(): void;
    initialize(): void;
    lazy_initialize(): Promise<void>;
    protected _width_policy(): SizingPolicy;
    protected _height_policy(): SizingPolicy;
    _update_layout(): void;
    get axis_views(): AxisView[];
    set_toolbar_visibility(visible: boolean): void;
    update_range(range_info: RangeInfo | null, options?: RangeOptions): void;
    reset_range(): void;
    trigger_ranges_update_event(): void;
    get_selection(): Map<DataRenderer, Selection>;
    update_selection(selections: Map<DataRenderer, Selection> | null): void;
    reset_selection(): void;
    protected _invalidate_layout(): void;
    get_renderer_views(): RendererView[];
    protected _compute_renderers(): Generator<Renderer, void, undefined>;
    build_renderer_views(): Promise<void>;
    build_tool_views(): Promise<void>;
    connect_signals(): void;
    has_finished(): boolean;
    after_layout(): void;
    repaint(): void;
    paint(): void;
    protected _actual_paint(): void;
    protected _paint_levels(ctx: Context2d, level: RenderLevel, clip_region: FrameBox, global_clip: boolean): void;
    protected _paint_layout(ctx: Context2d, layout: Layoutable): void;
    protected _map_hook(_ctx: Context2d, _frame_box: FrameBox): void;
    protected _paint_empty(ctx: Context2d, frame_box: FrameBox): void;
    protected _paint_outline(ctx: Context2d, frame_box: FrameBox): void;
    to_blob(): Promise<Blob>;
    export(type: "png" | "svg", hidpi?: boolean): CanvasLayer;
    serializable_state(): SerializableState;
}
//# sourceMappingURL=plot_canvas.d.ts.map