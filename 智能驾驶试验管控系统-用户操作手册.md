# 智能驾驶试验管控系统 - 用户操作手册

## 📋 文档概述

**软件名称**: 智能驾驶试验管控系统
**版本**: 1.0
**文档类型**: 用户操作手册
**编写日期**: 2025年8月3日
**适用对象**: 最终用户、试验工程师、数据分析师

## 🎯 软件简介

智能驾驶试验管控系统是一款专业的试验数据管理软件，专为智能驾驶汽车试验团队设计。软件提供完整的试验数据录入、管理、分析和可视化功能，帮助用户高效管理试验过程中的各类数据，提升试验管理效率。

### 主要功能特色
- ✅ **多表数据管理**: 支持试验问题、原始记录、行驶记录等8种数据表
- ✅ **智能数据录入**: 提供表格编辑、批量导入、数据验证等功能
- ✅ **数据可视化**: 内置图表创建器和看板设计器，支持多种图表类型
- ✅ **项目管理**: 支持项目和文件的组织管理，便于数据分类
- ✅ **公式计算**: 支持列级别公式计算，自动处理数据关联
- ✅ **数据同步**: 支持表间数据传递和Excel文件导入导出

## 🚀 快速开始

### 系统要求
- **操作系统**: Windows 10/11
- **内存**: 4GB以上
- **硬盘空间**: 2GB可用空间
- **显示器**: 1920×1080分辨率（推荐）

### 启动软件

#### 方法一：双击启动脚本
1. 找到软件安装目录
2. 双击 `offline_launcher.bat` 文件
3. 等待软件启动完成

#### 方法二：Python命令启动
1. 打开命令提示符
2. 切换到软件目录：`cd 软件安装路径`
3. 执行启动命令：`python run.py`

### 首次使用设置
1. **启动软件**后，系统会显示启动画面
2. **主界面加载**完成后，可以看到多个标签页
3. **创建项目**：点击菜单栏"项目" → "新建项目"
4. **选择工作目录**并输入项目名称
5. **开始使用**各项功能

## 🖥️ 界面布局介绍

### 主窗口结构

```
┌─────────────────────────────────────────────────────────────┐
│  菜单栏: 项目 文件 编辑 导入 导出 视图 数据传递 分析 快捷键 设置  │
├─────────────────────────────────────────────────────────────┤
│  工具栏: [新建项目] [保存] [撤销] [重做] [查找] [筛选] [数据分析] │
├─────────────────────────────────────────────────────────────┤
│ 项目面板 │              主要工作区域                        │
│ ┌─────┐ │  ┌─────────────────────────────────────────────┐ │
│ │项目树│ │  │ 标签页: [原始记录表][试验问题表][行驶记录表]  │ │
│ │     │ │  ├─────────────────────────────────────────────┤ │
│ │文件列│ │  │                                           │ │
│ │     │ │  │            数据表格区域                    │ │
│ │     │ │  │                                           │ │
│ └─────┘ │  └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ 快速筛选面板: [筛选条件] [重置] [查询] [筛选器] [折叠/展开]    │
├─────────────────────────────────────────────────────────────┤
│ 状态栏: 就绪 | 记录数: 0 | 选中: 0 | 时间: 2025-08-03 22:36  │
└─────────────────────────────────────────────────────────────┘
```

### 界面元素说明

#### 1. 菜单栏
- **项目**: 新建、打开、保存项目
- **文件**: 新建、打开、删除文件
- **编辑**: 撤销、重做、复制、粘贴
- **导入**: Excel文件导入、模板导入
- **导出**: 数据导出、报告生成
- **视图**: 界面布局控制
- **数据传递**: 表间数据同步
- **分析**: 数据可视化功能
- **快捷键**: 常用操作快捷方式
- **设置**: 系统配置和偏好设置

#### 2. 工具栏
- **新建项目**: 创建新的项目
- **保存**: 保存当前数据
- **撤销/重做**: 操作历史管理
- **查找**: 数据搜索功能
- **筛选**: 数据过滤功能
- **数据分析**: 打开可视化模块

#### 3. 项目面板（左侧）
- **项目树**: 显示当前项目结构
- **文件列表**: 显示项目中的文件

#### 4. 主工作区域（中央）
- **标签页**: 不同数据表的切换
- **数据表格**: 数据的显示和编辑区域

#### 5. 快速筛选面板（底部）
- **筛选条件**: 设置数据过滤条件
- **操作按钮**: 重置、查询、配置等

#### 6. 状态栏（底部）
- **系统状态**: 显示当前操作状态
- **统计信息**: 记录数、选中数等
- **时间信息**: 当前系统时间

## 📊 数据表管理

### 支持的数据表类型

系统支持以下8种数据表，每种表格都有专门的功能和用途：

| 表格名称 | 主要用途 | 关键字段 |
|---------|---------|---------|
| **试验问题表** | 记录试验中发现的问题 | 问题描述、严重度、状态、提出人 |
| **原始记录表** | 存储原始试验数据 | 样车编号、问题简述、有效性 |
| **行驶记录表** | 记录车辆行驶信息 | 试验里程、驾驶员、测试员 |
| **试验状态确认表** | 确认试验状态 | 零部件名称、软件版本号 |
| **零部件换装表** | 记录零部件更换 | 换装零部件、换装原因 |
| **试验日志表** | 记录试验日志 | 日志内容、记录时间 |
| **费用记录表** | 管理试验费用 | 费用项目、金额、负责人 |
| **典型场景表** | 管理典型场景 | 场景描述、测试功能 |

### 基本操作

#### 1. 切换数据表
- **方法一**: 点击主工作区域顶部的标签页
- **方法二**: 使用快捷键 `Ctrl+Tab` 在标签页间切换
- **方法三**: 右键点击标签页可进行标签页管理

#### 2. 新增记录
1. 选择要操作的数据表标签页
2. 点击工具栏中的 **"新增记录"** 按钮
3. 在弹出的编辑对话框中填写数据
4. 点击 **"确定"** 保存记录

#### 3. 编辑记录
1. 在表格中双击要编辑的单元格
2. 直接在单元格中修改数据
3. 按 `Enter` 键确认修改
4. 或右键点击行，选择 **"编辑记录"**

#### 4. 删除记录
1. 选中要删除的行（可多选）
2. 右键点击选择 **"删除记录"**
3. 或点击工具栏的 **"删除记录"** 按钮
4. 确认删除操作

#### 5. 批量操作
- **全选**: `Ctrl+A` 选择所有记录
- **多选**: 按住 `Ctrl` 键点击多行
- **范围选择**: 按住 `Shift` 键选择连续行
- **复制**: `Ctrl+C` 复制选中数据
- **粘贴**: `Ctrl+V` 粘贴数据

## 🔍 数据查找与筛选

### 快速查找

#### 1. 使用查找功能
1. 点击工具栏的 **"查找"** 按钮（🔍）
2. 在弹出的查找对话框中输入关键词
3. 选择查找范围（当前列、所有列、特定字段）
4. 点击 **"查找下一个"** 或 **"查找全部"**
5. 系统会高亮显示匹配的结果

#### 2. 查找选项
- **匹配大小写**: 区分大小写查找
- **全字匹配**: 完全匹配单词
- **正则表达式**: 使用正则表达式查找
- **查找方向**: 向上或向下查找

### 数据筛选

#### 1. 快速筛选面板
位于主界面底部的筛选面板提供便捷的筛选功能：

1. **展开筛选面板**: 点击 **"+ 添加筛选条件"** 按钮
2. **添加筛选条件**:
   - 选择字段名称
   - 选择比较操作符（等于、包含、大于等）
   - 输入筛选值
3. **应用筛选**: 点击 **"查询"** 按钮
4. **重置筛选**: 点击 **"重置"** 按钮清除所有条件

#### 2. 高级筛选
1. 点击筛选面板中的 **"筛选器"** 按钮
2. 在高级筛选对话框中设置复杂条件
3. 支持多条件组合（AND、OR逻辑）
4. 可保存常用筛选条件为模板

#### 3. 筛选操作符说明

| 操作符 | 说明 | 示例 |
|-------|------|------|
| **等于** | 完全匹配 | 状态 = "已完成" |
| **不等于** | 不匹配 | 严重度 ≠ "低" |
| **包含** | 部分匹配 | 描述 包含 "制动" |
| **不包含** | 不包含指定内容 | 描述 不包含 "测试" |
| **大于** | 数值比较 | 里程 > 100 |
| **小于** | 数值比较 | 费用 < 1000 |
| **介于** | 范围筛选 | 日期 介于 2025-01-01 和 2025-12-31 |
| **为空** | 空值筛选 | 备注 为空 |
| **不为空** | 非空值筛选 | 图片路径 不为空 |

## 📁 项目与文件管理

### 项目管理

#### 1. 创建新项目
1. 点击菜单栏 **"项目"** → **"新建项目"**
2. 在弹出的对话框中选择项目存储目录
3. 输入项目名称（建议使用有意义的名称）
4. 点击 **"确定"** 创建项目
5. 系统会自动切换到新创建的项目

#### 2. 打开现有项目
1. 点击菜单栏 **"项目"** → **"打开项目"**
2. 浏览并选择项目文件（.tmsproject格式）
3. 点击 **"打开"** 加载项目
4. 系统会恢复项目的所有数据和设置

#### 3. 保存项目
- **自动保存**: 系统会定期自动保存项目数据
- **手动保存**: 点击工具栏 **"保存"** 按钮或使用快捷键 `Ctrl+S`
- **另存为**: 菜单栏 **"项目"** → **"另存为"** 可将项目保存到新位置

#### 4. 项目信息管理
1. 右键点击项目面板中的项目名称
2. 选择 **"项目属性"** 查看和编辑项目信息
3. 可修改项目名称、描述、创建时间等信息

### 文件管理

#### 1. 新建文件
1. 点击菜单栏 **"文件"** → **"新建文件"**
2. 选择文件保存位置和文件名
3. 系统会创建一个空的Excel文件模板
4. 新文件会自动添加到当前项目中

#### 2. 打开文件
1. 点击菜单栏 **"文件"** → **"打开文件"**
2. 选择要打开的Excel文件
3. 系统会解析文件内容并显示在相应的数据表中
4. 文件会添加到项目文件列表中

#### 3. 删除文件
1. 在项目面板的文件列表中选择要删除的文件
2. 右键点击选择 **"删除文件"**
3. 确认删除操作（注意：这会从项目中移除文件引用）

## 📥 数据导入导出

### Excel文件导入

#### 1. 导入Excel数据
1. 点击菜单栏 **"导入"** → **"导入Excel文件"**
2. 选择要导入的Excel文件
3. 在字段映射对话框中：
   - 选择目标数据表
   - 设置字段对应关系
   - 配置导入选项
4. 点击 **"开始导入"** 执行导入操作
5. 查看导入结果报告

#### 2. 字段映射设置
- **自动映射**: 系统会根据列名自动匹配字段
- **手动映射**: 可手动调整字段对应关系
- **数据预览**: 可预览导入数据的前几行
- **验证规则**: 设置数据验证规则，确保数据质量

#### 3. 导入选项配置
- **跳过空行**: 忽略Excel中的空行
- **重复数据处理**: 选择跳过、覆盖或提示
- **错误处理**: 设置遇到错误时的处理方式
- **批量大小**: 设置每批导入的记录数

### 数据导出

#### 1. 导出当前表格数据
1. 选择要导出的数据表标签页
2. 点击菜单栏 **"导出"** → **"导出当前表格"**
3. 选择导出格式（Excel、CSV、PDF）
4. 设置导出选项：
   - 导出范围（全部数据、选中数据、筛选结果）
   - 包含的字段
   - 文件名和保存位置
5. 点击 **"导出"** 生成文件

#### 2. 批量导出
1. 点击菜单栏 **"导出"** → **"批量导出"**
2. 选择要导出的多个数据表
3. 设置统一的导出格式和选项
4. 系统会生成包含多个工作表的Excel文件

#### 3. 报告导出
1. 点击菜单栏 **"导出"** → **"生成报告"**
2. 选择报告模板或自定义报告格式
3. 设置报告参数（时间范围、数据筛选等）
4. 生成PDF或Word格式的分析报告

## 🔄 数据传递与同步

### 表间数据传递

系统支持在不同数据表之间传递数据，特别是原始记录表和试验问题表之间的数据同步。

#### 1. 原始记录同步到试验问题
1. 在原始记录表中选择要同步的记录
2. 点击工具栏的 **"原始记录同步"** 按钮
3. 在同步对话框中：
   - 确认要同步的字段映射
   - 设置同步选项（新增、更新、跳过重复）
   - 预览同步结果
4. 点击 **"开始同步"** 执行操作

#### 2. 试验问题同步到原始记录
1. 在试验问题表中选择要同步的记录
2. 使用菜单栏 **"数据传递"** → **"同步到原始记录"**
3. 配置同步参数并执行

#### 3. 字段映射配置
- **默认映射**: 系统提供预设的字段映射关系
- **自定义映射**: 可根据需要调整映射关系
- **映射保存**: 可保存常用的映射配置为模板

### 数据验证

#### 1. 数据完整性检查
- **必填字段检查**: 确保关键字段不为空
- **数据格式验证**: 检查日期、数字等格式是否正确
- **数据范围验证**: 检查数值是否在合理范围内
- **重复数据检测**: 识别可能的重复记录

#### 2. 数据质量报告
1. 点击菜单栏 **"分析"** → **"数据质量检查"**
2. 系统会生成数据质量报告，包括：
   - 数据完整性统计
   - 异常数据列表
   - 数据分布分析
   - 改进建议

## 📊 数据可视化

### 图表创建

#### 1. 打开图表创建器
1. 点击工具栏的 **"数据分析"** 按钮
2. 在数据可视化窗口中选择 **"图表管理"** 标签页
3. 点击 **"创建图表"** 按钮

#### 2. 选择图表类型
系统支持多种图表类型：
- **柱状图**: 适合比较不同类别的数值
- **折线图**: 适合显示趋势变化
- **饼图**: 适合显示比例关系
- **散点图**: 适合显示相关性
- **雷达图**: 适合多维度比较
- **热力图**: 适合显示数据密度
- **仪表盘**: 适合显示关键指标

#### 3. 配置图表参数
1. **数据源设置**:
   - 选择数据表
   - 设置数据筛选条件
   - 选择时间范围
2. **图表配置**:
   - 设置X轴和Y轴字段
   - 配置图表标题和标签
   - 选择颜色主题
3. **高级选项**:
   - 设置图例位置
   - 配置数据标签显示
   - 设置交互选项

#### 4. 保存和管理图表
- **保存图表**: 将图表配置保存为模板
- **图表列表**: 查看和管理已创建的图表
- **图表编辑**: 修改现有图表的配置
- **图表删除**: 删除不需要的图表

### 看板设计

#### 1. 创建看板
1. 在数据可视化窗口中选择 **"看板管理"** 标签页
2. 点击 **"创建看板"** 按钮
3. 输入看板名称和描述

#### 2. 看板编辑器
看板编辑器提供拖拽式的设计界面：
- **左侧面板**: 图表组件库和属性设置
- **中央画布**: 看板设计区域
- **右侧面板**: 资源管理和预览

#### 3. 添加图表组件
1. 从左侧组件库拖拽图表到画布
2. 调整图表大小和位置
3. 配置图表数据源和显示参数
4. 设置图表样式和交互效果

#### 4. 看板布局
- **网格布局**: 使用网格系统对齐组件
- **自由布局**: 自由拖拽组件位置
- **响应式设计**: 支持不同屏幕尺寸自适应

### 看板查看

#### 1. 查看看板
1. 在看板管理中选择要查看的看板
2. 点击 **"查看看板"** 按钮
3. 看板会在新窗口中打开

#### 2. 交互功能
- **图表点击**: 点击图表元素可查看详细数据
- **数据筛选**: 使用筛选控件过滤数据
- **时间范围**: 调整时间范围查看不同时期的数据
- **数据刷新**: 手动或自动刷新数据

#### 3. 看板导出
- **图片导出**: 将看板导出为PNG或JPG图片
- **PDF导出**: 生成PDF格式的看板报告
- **HTML导出**: 导出为可在浏览器中查看的HTML文件

## ⚙️ 系统设置

### 界面设置

#### 1. 主题设置
1. 点击菜单栏 **"设置"** → **"主题设置"**
2. 选择喜欢的界面主题：
   - **浅色主题**: 适合明亮环境使用
   - **深色主题**: 适合暗光环境使用
   - **自动主题**: 根据系统设置自动切换

#### 2. 字体设置
- **字体大小**: 调整界面字体大小
- **字体类型**: 选择合适的字体
- **字体颜色**: 自定义字体颜色

#### 3. 布局设置
- **面板显示**: 控制各个面板的显示/隐藏
- **工具栏配置**: 自定义工具栏按钮
- **标签页顺序**: 调整数据表标签页的顺序

### 数据设置

#### 1. 字段显示设置
1. 右键点击数据表的列头
2. 选择 **"显示设置"**
3. 在设置对话框中：
   - 选择要显示的字段
   - 调整字段显示顺序
   - 设置列宽和对齐方式

#### 2. 数据验证规则
1. 点击菜单栏 **"设置"** → **"数据验证"**
2. 为不同字段设置验证规则：
   - **数据类型**: 文本、数字、日期等
   - **取值范围**: 最小值、最大值
   - **格式要求**: 正则表达式验证
   - **必填设置**: 设置必填字段

#### 3. 默认值设置
- **新记录默认值**: 为新增记录设置默认值
- **计算字段**: 设置自动计算的字段
- **时间戳**: 自动记录创建和修改时间

### 权限设置

#### 1. 管理员模式
1. 点击菜单栏 **"设置"** → **"管理员模式"**
2. 输入管理员密码
3. 在管理员模式下可以：
   - 修改系统配置
   - 管理用户权限
   - 初始化数据库
   - 查看系统日志

#### 2. 字段权限
- **只读字段**: 设置某些字段为只读
- **隐藏字段**: 隐藏敏感字段
- **编辑权限**: 控制字段的编辑权限

## 🔧 公式计算

### 列级公式

#### 1. 设置列公式
1. 右键点击要设置公式的列头
2. 选择 **"设置列公式"**
3. 在公式编辑器中输入公式
4. 点击 **"确定"** 应用公式

#### 2. 公式语法
- **字段引用**: `={字段名}` 引用其他字段的值
- **算术运算**: `+`、`-`、`*`、`/` 进行数学计算
- **条件判断**: `IF(条件, 真值, 假值)` 条件判断
- **文本处理**: `CONCAT(文本1, 文本2)` 文本连接
- **聚合函数**: `SUM(字段)` 求和、`AVG(字段)` 平均值

#### 3. 公式示例
```
={数量} * {单价}                    # 计算总价
=IF({状态}=='完成', 'DONE', 'PENDING')  # 状态转换
=CONCAT({前缀}, '-', {编号})        # 生成编码
=SUM({金额})                       # 计算总金额
=ROUND({数值}, 2)                  # 四舍五入到2位小数
```

#### 4. 公式管理
- **公式验证**: 检查公式语法是否正确
- **依赖分析**: 显示字段间的依赖关系
- **公式备份**: 自动备份公式配置
- **批量应用**: 将公式应用到整列数据

## ❓ 常见问题解答

### 启动和安装问题

**Q: 软件启动失败，提示缺少Python环境？**
A: 请确保使用软件自带的Python环境，双击 `offline_launcher.bat` 启动，或检查Python环境是否完整。

**Q: 启动时出现"模块导入失败"错误？**
A: 这通常是依赖包问题，请尝试重新安装软件或联系技术支持。

**Q: 软件界面显示异常或乱码？**
A: 请检查系统的显示设置，确保DPI缩放设置合适，建议使用100%或125%缩放。

### 数据操作问题

**Q: 导入Excel文件时提示格式错误？**
A: 请确保Excel文件格式正确，建议使用.xlsx格式，并检查数据中是否有特殊字符。

**Q: 数据保存后丢失？**
A: 请检查是否有足够的磁盘空间，并确保对软件目录有写入权限。建议定期备份数据库文件。

**Q: 筛选功能不生效？**
A: 请检查筛选条件是否设置正确，注意数据类型匹配，日期格式要正确。

**Q: 公式计算结果不正确？**
A: 请检查公式语法，确保引用的字段名称正确，注意数据类型转换。

### 性能问题

**Q: 软件运行缓慢？**
A: 建议关闭不必要的程序，增加系统内存，或减少同时显示的数据量。

**Q: 大量数据导入很慢？**
A: 可以分批导入数据，或在导入前关闭不必要的功能如实时计算。

### 功能使用问题

**Q: 如何备份数据？**
A: 可以通过"项目"菜单的"另存为"功能备份整个项目，或直接复制数据库文件。

**Q: 如何恢复误删的数据？**
A: 可以使用"编辑"菜单的"撤销"功能，或从备份文件中恢复数据。

**Q: 图表显示不正常？**
A: 请检查数据源设置，确保字段类型正确，并尝试刷新图表数据。

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **用户手册**: 查看软件内置帮助文档
- **在线帮助**: 访问官方网站获取最新信息

### 问题反馈
在反馈问题时，请提供以下信息：
1. 软件版本号
2. 操作系统版本
3. 详细的问题描述
4. 重现步骤
5. 相关的错误截图
6. 日志文件（位于app/logs目录）

### 更新说明
- 软件会定期发布更新版本
- 更新内容包括功能改进、问题修复和性能优化
- 建议定期检查并安装最新版本

---

**文档版本**: 1.0
**最后更新**: 2025年8月3日
**适用软件版本**: 智能驾驶试验管控系统 v1.0