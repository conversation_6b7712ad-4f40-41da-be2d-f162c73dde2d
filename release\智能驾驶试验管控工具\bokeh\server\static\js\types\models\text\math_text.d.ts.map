{"version": 3, "file": "math_text.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/models/text/math_text.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,CAAC,8BAAuB;AACpC,OAAO,KAAK,OAAO,2BAAoB;AAEvC,OAAO,EAAC,SAAS,EAAC,+BAAwB;AAI1C,OAAO,EAAC,IAAI,EAAC,yBAAkB;AAC/B,OAAO,EAAC,WAAW,EAAE,gBAAgB,EAAc,QAAQ,EAAC,4BAAqB;AAEjF,OAAO,EAAkB,IAAI,EAAC,+BAAwB;AACtD,OAAO,EAAC,IAAI,EAAC,6BAAsB;AACnC,OAAO,EAAC,QAAQ,EAAE,YAAY,EAAC,MAAM,aAAa,CAAA;AAClD,OAAO,EAAC,eAAe,EAAmB,MAAM,aAAa,CAAA;AAE7D;;GAEG;AACH,8BAAsB,YAAa,SAAQ,YAAa,YAAW,WAAW;IACnE,KAAK,EAAE,QAAQ,CAAA;IAExB,QAAQ,IAAI,WAAW;IAIvB,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,SAAS,EAAE,QAAQ,CAAiB;IAIpC,KAAK,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO,GAAG,SAAS,CAAS;IAEvD,iBAAiB,IAAI,gBAAgB;IAIrC,SAAS,EAAE,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAS;IAC/C,SAAS,EAAE,KAAK,GAAI,QAAQ,GAAG,UAAU,GAAG,QAAQ,CAAW;IAE/D,eAAe,EAAE,MAAM,CAAK;IAE5B,IAAI,cAAc,CAAC,CAAC,EAAE,MAAM,GAAG,IAAI,GAAG,SAAS,EAG9C;IAED,IAAI,cAAc,IAAI,MAAM,CAE3B;IAED,eAAe,EAAE,MAAM,CAAM;IAC7B,OAAO,CAAC,IAAI,CAAQ;IACpB,OAAO,CAAC,KAAK,CAAQ;IACrB,OAAO,CAAC,SAAS,CAA2B;IAC5C,OAAO,CAAC,WAAW,CAAY;IAE/B,IAAI,gBAAgB,IAAI,OAAO,CAE9B;IAED,KAAK,IAAI,IAAI;IASb,IAAI,QAAQ,CAAC,CAAC,EAAE,QAAQ,EAEvB;IAED,IAAI,QAAQ,IAAI,QAAQ,CAEvB;IAED,IAAI,IAAI,IAAI,MAAM,CAEjB;IAED,IAAI,QAAQ,IAAI,eAAe,CAE9B;IAEc,eAAe;IAarB,eAAe,IAAI,IAAI;IAKhC,IAAI,OAAO,CAAC,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,EAsBpC;IAED;;;OAGG;IACH,SAAS,CAAC,kBAAkB,IAAI;QAAC,CAAC,EAAE,MAAM,CAAC;QAAC,CAAC,EAAE,MAAM,CAAA;KAAC;IAgCtD;;MAEE;IACF,IAAI,IAAI,IAAI;IAiBZ,OAAO,CAAC,mBAAmB;IAO3B,OAAO,CAAC,oBAAoB;IAmB5B,KAAK,IAAI,IAAI;IAIb,IAAI,IAAI,IAAI;IAWZ,IAAI,IAAI,IAAI;IAeZ,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI;IAgBhC,UAAU,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI;IAgBhC,SAAS,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;YAEzD,UAAU;IA8BxB;;;MAGE;IACF,KAAK,CAAC,GAAG,EAAE,SAAS,GAAG,IAAI;CA6B5B;AAED,yBAAiB,QAAQ,CAAC;IACxB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG;QACnC,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;KACzB,CAAA;CACF;AAED,MAAM,WAAW,QAAS,SAAQ,QAAQ,CAAC,KAAK;CAAG;AAEnD,qBAAa,QAAS,SAAQ,QAAQ;IAC3B,UAAU,EAAE,QAAQ,CAAC,KAAK,CAAA;IAC1B,aAAa,EAAE,YAAY,CAAA;gBAExB,KAAK,CAAC,EAAE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC;CAG5C;AAED,qBAAa,SAAU,SAAQ,YAAY;IAChC,KAAK,EAAE,KAAK,CAAA;IAErB,SAAS,CAAC,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;CAGhE;AAED,yBAAiB,KAAK,CAAC;IACrB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAA;CACnC;AAED,MAAM,WAAW,KAAM,SAAQ,KAAK,CAAC,KAAK;CAAG;AAE7C,qBAAa,KAAM,SAAQ,QAAQ;IACxB,UAAU,EAAE,KAAK,CAAC,KAAK,CAAA;IACvB,aAAa,EAAE,SAAS,CAAA;gBAErB,KAAK,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;CAOzC;AAED,qBAAa,UAAW,SAAQ,YAAY;IACjC,KAAK,EAAE,MAAM,CAAA;IAEtB,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;CAG/D;AAED,yBAAiB,MAAM,CAAC;IACtB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IACpC,KAAY,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAA;CACnC;AAED,MAAM,WAAW,MAAO,SAAQ,MAAM,CAAC,KAAK;CAAG;AAE/C,qBAAa,MAAO,SAAQ,QAAQ;IACzB,UAAU,EAAE,MAAM,CAAC,KAAK,CAAA;IACxB,aAAa,EAAE,UAAU,CAAA;gBAEtB,KAAK,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;CAO1C;AAED,qBAAa,OAAQ,SAAQ,YAAY;IAC9B,KAAK,EAAE,GAAG,CAAA;IAEnB,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,GAAG,WAAW,GAAG,SAAS;CAI/D;AAED,yBAAiB,GAAG,CAAC;IACnB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG;QACnC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC;YAAC,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;SAAC,CAAC,CAAA;QAC9D,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;KAC5B,CAAA;CACF;AAED,MAAM,WAAW,GAAI,SAAQ,GAAG,CAAC,KAAK;CAAG;AAEzC,qBAAa,GAAI,SAAQ,QAAQ;IACtB,UAAU,EAAE,GAAG,CAAC,KAAK,CAAA;IACrB,aAAa,EAAE,OAAO,CAAA;gBAEnB,KAAK,CAAC,EAAE,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC;CAYvC"}