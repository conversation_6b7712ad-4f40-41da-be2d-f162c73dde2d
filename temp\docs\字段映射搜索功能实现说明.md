# 字段映射搜索功能实现说明

## 功能概述

根据用户需求，为应用程序的字段映射功能添加了搜索功能，包括：

1. **导入字段匹配的目标字段搜索**
2. **数据传递字段匹配的源字段和目标字段搜索**

## 实现方案

### 1. 创建可搜索的ComboBox组件

**文件位置**: `src/views/custom_widgets.py`

新增了 `SearchableComboBox` 类，主要特性：

- **可编辑输入**: 将ComboBox设置为可编辑模式，用户可以直接输入文本
- **实时过滤**: 根据用户输入实时过滤并显示匹配的字段选项
- **智能排序**: 搜索结果按相关性排序（完全匹配 > 前缀匹配 > 包含匹配）
- **模糊匹配**: 支持包含用户输入字符串的模糊匹配
- **不区分大小写**: 搜索功能不区分大小写
- **无匹配提示**: 当没有匹配结果时显示"无匹配项"提示
- **保留原功能**: 完全保留原有的下拉选择功能

#### 核心方法

```python
def filter_items(self, text):
    """根据输入文本过滤项目"""
    # 按相关性排序：完全匹配 > 前缀匹配 > 包含匹配
    
def _update_items(self, items):
    """更新下拉框项目，避免信号递归"""
    
def setCurrentText(self, text):
    """设置当前文本，确保正确显示"""
```

### 2. 修改字段映射对话框

**文件位置**: `src/views/dialogs.py`

#### 2.1 导入SearchableComboBox组件

```python
# 导入可搜索的ComboBox组件
try:
    from .custom_widgets import SearchableComboBox
except ImportError:
    # 如果导入失败，使用普通的QComboBox作为备用
    SearchableComboBox = QComboBox
```

#### 2.2 修改FieldMappingDialog（导入字段匹配）

**修改位置**:
- `set_mapping_config` 方法中的目标字段下拉框创建
- `add_mapping_row` 方法中的目标字段下拉框创建
- `_update_target_field_combos` 方法中的字段更新逻辑

**修改内容**:
```python
# 目标字段下拉框 - 使用可搜索的ComboBox
target_combo = SearchableComboBox()
target_combo.addItem("")  # 添加空选项
target_combo.addItems(self.target_fields)
```

#### 2.3 修改TransferFieldMappingDialog（数据传递字段匹配）

**修改位置**:
- `add_mapping_row` 方法中的源字段和目标字段下拉框创建
- `set_mapping_config` 方法中的字段设置逻辑
- `_update_source_field_combos` 和 `_update_target_field_combos` 方法

**修改内容**:
```python
# 源字段下拉框 - 使用可搜索的ComboBox
source_field_combo = SearchableComboBox()
source_field_combo.addItems([''] + self.source_fields)

# 目标字段下拉框 - 使用可搜索的ComboBox
target_field_combo = SearchableComboBox()
target_field_combo.addItems([''] + self.target_fields)
```

## 功能特性

### 1. 搜索功能特性

- **实时搜索**: 用户输入时立即过滤结果
- **智能匹配**: 支持完全匹配、前缀匹配、包含匹配
- **相关性排序**: 搜索结果按匹配度排序
- **大小写不敏感**: 搜索时忽略大小写
- **空值处理**: 正确处理空字符串和空选项

### 2. 用户体验优化

- **保留原功能**: 完全兼容原有的下拉选择功能
- **无匹配提示**: 当搜索无结果时显示友好提示
- **文本恢复**: 切换数据表时正确恢复之前的选择
- **信号管理**: 避免递归调用和不必要的事件触发

### 3. 向后兼容性

- **备用机制**: 如果SearchableComboBox导入失败，自动使用普通QComboBox
- **接口一致**: 与原有QComboBox接口完全兼容
- **配置保持**: 现有的字段映射配置文件继续有效

## 使用场景

### 场景1：导入字段匹配的目标字段搜索

**位置**: 菜单栏 -> 导入 -> 导入字段匹配 -> 字段映射配置

**使用方法**:
1. 在目标字段下拉框中直接输入字段名称的部分内容
2. 系统实时显示匹配的字段选项
3. 从过滤后的结果中选择所需字段

### 场景2：数据传递字段匹配的源字段和目标字段搜索

**位置**: 菜单栏 -> 数据传递 -> 导入字段匹配 -> 传递字段映射配置

**使用方法**:
1. 在源字段下拉框中输入源字段名称进行搜索
2. 在目标字段下拉框中输入目标字段名称进行搜索
3. 快速定位和选择所需的字段进行映射

## 测试验证

创建了测试脚本 `test_searchable_combobox.py` 用于验证功能：

- 测试搜索功能的正确性
- 验证排序逻辑
- 检查用户交互体验
- 确认向后兼容性

## 技术细节

### 1. 信号处理

为避免递归调用和不必要的事件触发，在关键操作时临时断开和重连信号：

```python
# 暂时断开信号连接
self.lineEdit().textChanged.disconnect(self.filter_items)
# 执行操作
# 重新连接信号
self.lineEdit().textChanged.connect(self.filter_items)
```

### 2. 文本恢复机制

在更新字段列表时保持用户的当前选择：

```python
current_text = combo.currentText()
combo.clear()
combo.addItems(new_items)
combo.setCurrentText(current_text)
```

### 3. 错误处理

提供了完善的错误处理和备用机制，确保功能的稳定性。

## 预期效果

用户现在可以：

1. **快速搜索**: 通过输入字段名称的部分内容快速定位所需字段
2. **提高效率**: 大幅提升字段映射配置的效率，特别是在字段数量较多时
3. **减少错误**: 通过搜索功能减少手动查找字段时的错误
4. **保持习惯**: 仍可使用原有的下拉选择方式，无需改变操作习惯

## 注意事项

1. **性能考虑**: 在字段数量极大时，搜索性能仍然良好
2. **内存管理**: 正确管理原始字段列表，避免内存泄漏
3. **事件处理**: 妥善处理各种用户交互事件，避免界面卡顿
4. **兼容性**: 确保与现有代码的完全兼容性
