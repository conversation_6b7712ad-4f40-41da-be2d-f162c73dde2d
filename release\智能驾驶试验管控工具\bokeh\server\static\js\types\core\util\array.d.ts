import { Arrayable } from "../types";
import { map, reduce, min, min_by, max, max_by, sum, cumsum, every, some, find, find_last, find_index, find_last_index, sorted_index, is_empty } from "./arrayable";
export { map, reduce, min, min_by, max, max_by, sum, cumsum, every, some, find, find_last, find_index, find_last_index, sorted_index, is_empty };
export declare function head<T>(array: T[]): T;
export declare function tail<T>(array: ArrayLike<T>): T;
export declare function last<T>(array: ArrayLike<T>): T;
export declare function copy<T>(array: T[]): T[];
export declare function concat<T>(arrays: T[][]): T[];
export declare function includes<T>(array: T[], value: T): boolean;
export declare const contains: typeof includes;
export declare function nth<T>(array: T[], index: number): T;
export declare function zip<A, B>(As: A[], Bs: B[]): [A, B][];
export declare function zip<A, B, C>(As: A[], Bs: B[], Cs: C[]): [A, B, C][];
export declare function zip<T>(...arrays: T[][]): T[][];
export declare function unzip<A, B>(ABs: [A, B][]): [A[], B[]];
export declare function unzip<A, B, C>(ABCs: [A, B, C][]): [A[], B[], C[]];
export declare function unzip<T>(arrays: T[][]): T[][];
export declare function range(start: number, stop?: number, step?: number): number[];
export declare function linspace(start: number, stop: number, num?: number): number[];
export declare function transpose<T>(array: T[][]): T[][];
export declare function argmin(array: number[]): number;
export declare function argmax(array: number[]): number;
export declare function sort_by<T>(array: T[], key: (item: T) => number): T[];
export declare function uniq<T>(array: Arrayable<T>): T[];
export declare function uniq_by<T, U>(array: T[], key: (item: T) => U): T[];
export declare function union<T>(...arrays: T[][]): T[];
export declare function intersection<T>(array: T[], ...arrays: T[][]): T[];
export declare function difference<T>(array: T[], ...arrays: T[][]): T[];
export declare function remove_at<T>(array: T[], i: number): T[];
export declare function remove_by<T>(array: T[], key: (item: T) => boolean): void;
export declare function shuffle<T>(array: T[]): T[];
export declare function pairwise<T, U>(array: T[], fn: (prev: T, next: T) => U): U[];
export declare function reversed<T>(array: T[]): T[];
export declare function repeat<T>(value: T, n: number): T[];
//# sourceMappingURL=array.d.ts.map