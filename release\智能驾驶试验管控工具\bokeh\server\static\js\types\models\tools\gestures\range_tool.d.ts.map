{"version": 3, "file": "range_tool.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/lib/models/tools/gestures/range_tool.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,QAAQ,EAAC,gCAAsB;AACvC,OAAO,EAAC,aAAa,EAAiB,MAAM,kCAAkC,CAAA;AAC9E,OAAO,EAAC,KAAK,EAAC,MAAM,oBAAoB,CAAA;AACxC,OAAO,EAAC,OAAO,EAAC,MAAM,sBAAsB,CAAA;AAC5C,OAAO,EAAC,KAAK,EAAC,MAAM,oBAAoB,CAAA;AAExC,OAAO,KAAK,CAAC,iCAAuB;AACpC,OAAO,EAAC,WAAW,EAAE,eAAe,EAAC,MAAM,gBAAgB,CAAA;AAG3D,0BAAkB,IAAI;IAAG,IAAI,IAAA;IAAE,IAAI,IAAA;IAAE,KAAK,IAAA;IAAE,SAAS,IAAA;IAAE,MAAM,IAAA;IAAE,GAAG,IAAA;IAAE,SAAS,IAAA;IAAE,kBAAkB,IAAA;CAAE;AAEnG,wBAAgB,SAAS,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI,CAQ1C;AAID,wBAAgB,OAAO,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAKjG;AAID,wBAAgB,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,aAAa,GAAG,OAAO,CAgB/G;AAED,wBAAgB,YAAY,CAAC,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,UAKpE;AAED,wBAAgB,aAAa,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,GAAG,MAAM,CAM/F;AAED,wBAAgB,qBAAqB,CAAC,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CASjF;AAED,wBAAgB,uBAAuB,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,CASrF;AAED,wBAAgB,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK,GAAG,IAAI,CAYjG;AAED,qBAAa,aAAc,SAAQ,eAAe;IACvC,KAAK,EAAE,SAAS,CAAA;IAEzB,OAAO,CAAC,OAAO,CAAQ;IACvB,OAAO,CAAC,OAAO,CAAQ;IACvB,OAAO,CAAC,IAAI,CAAM;IAET,UAAU,IAAI,IAAI;IAMlB,eAAe,IAAI,IAAI;IAQvB,UAAU,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI;IAwC9B,IAAI,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI;IAwCxB,QAAQ,CAAC,GAAG,EAAE,QAAQ,GAAG,IAAI;CAIvC;AAcD,yBAAiB,SAAS,CAAC;IACzB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG;QACtC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,CAAA;QACnC,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QAClC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,CAAA;QACnC,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QAClC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;KACnC,CAAA;CACF;AAED,MAAM,WAAW,SAAU,SAAQ,SAAS,CAAC,KAAK;CAAG;AAErD,qBAAa,SAAU,SAAQ,WAAW;IAC/B,UAAU,EAAE,SAAS,CAAC,KAAK,CAAA;IAC3B,aAAa,EAAE,aAAa,CAAA;IAE5B,OAAO,EAAE,aAAa,CAAA;gBAEnB,KAAK,CAAC,EAAE,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC;IAgBnC,UAAU,IAAI,IAAI;IAO3B,0BAA0B,IAAI,IAAI;IA0BzB,SAAS,SAAe;IACxB,IAAI,SAAkB;IACtB,UAAU,QAAiB;IAC3B,aAAa,SAAI;CAC3B"}