{"version": 3, "file": "legend.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/models/annotations/legend.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,UAAU,EAAE,cAAc,EAAC,MAAM,cAAc,CAAA;AACvD,OAAO,EAAC,UAAU,EAAC,MAAM,eAAe,CAAA;AACxC,OAAO,EAAC,WAAW,EAAE,cAAc,EAAE,iBAAiB,EAAC,yBAAkB;AACzE,OAAO,KAAK,OAAO,2BAAoB;AACvC,OAAO,KAAK,MAAM,mCAA4B;AAC9C,OAAO,KAAK,CAAC,8BAAuB;AACpC,OAAO,EAAC,OAAO,EAAC,6BAAsB;AACtC,OAAO,EAAC,IAAI,EAAC,0BAAmB;AAGhC,OAAO,EAAC,IAAI,EAAC,6BAAsB;AAGnC,OAAO,EAAC,SAAS,EAAC,+BAAwB;AAG1C,qBAAa,UAAW,SAAQ,cAAc;IACnC,KAAK,EAAE,MAAM,CAAA;IACb,OAAO,EAAE,MAAM,CAAC,OAAO,CAAA;IAEvB,aAAa,IAAI,IAAI;IAQ9B,SAAS,CAAC,gBAAgB,EAAE,MAAM,CAAA;IAClC,SAAS,CAAC,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAC1C,SAAS,CAAC,YAAY,EAAE,MAAM,CAAA;IAC9B,SAAS,CAAC,WAAW,EAAE,MAAM,CAAA;IAEpB,MAAM,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI;IAIxD,IAAI,cAAc,IAAI,MAAM,CAE3B;IAEQ,eAAe,IAAI,IAAI;IAMhC,mBAAmB,IAAI,IAAI;IAwG3B,gBAAgB,IAAI,IAAI;IAIf,eAAe,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,OAAO;IAKhD,MAAM,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,OAAO;IAqDhD,SAAS,CAAC,OAAO,IAAI,IAAI;IAwBzB,SAAS,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI;IAO5D,SAAS,CAAC,kBAAkB,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI;IA4D9D,SAAS,CAAC,WAAW,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI;cAYpC,SAAS,IAAI,IAAI;CAOrC;AAED,yBAAiB,MAAM,CAAC;IACtB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG;QACrC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;QACpC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,cAAc,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAA;QACvD,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,CAAA;QAChC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAClC,cAAc,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAClC,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAChC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC/B,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAChC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC/B,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC1B,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC3B,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAC3B,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAA;QAC/B,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAA;KAC5C,GAAG,MAAM,CAAA;IAEV,KAAY,MAAM,GAChB,MAAM,CAAC,SAAS,GAChB,MAAM,CAAC,SAAS,GAChB,MAAM,CAAC,YAAY,GACnB,MAAM,CAAC,UAAU,GACjB,MAAM,CAAC,cAAc,CAAA;IAEvB,KAAY,OAAO,GAAG,UAAU,CAAC,OAAO,GAAG;QACzC,UAAU,EAAE,OAAO,CAAC,IAAI,CAAA;QACxB,UAAU,EAAE,OAAO,CAAC,IAAI,CAAA;QACxB,aAAa,EAAE,OAAO,CAAC,IAAI,CAAA;QAC3B,WAAW,EAAE,OAAO,CAAC,IAAI,CAAA;QACzB,eAAe,EAAE,OAAO,CAAC,IAAI,CAAA;KAC9B,CAAA;CACF;AAED,MAAM,WAAW,MAAO,SAAQ,MAAM,CAAC,KAAK;CAAG;AAE/C,qBAAa,MAAO,SAAQ,UAAU;IAC3B,UAAU,EAAE,MAAM,CAAC,KAAK,CAAA;IACxB,aAAa,EAAE,UAAU,CAAA;IAElC,WAAW,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;gBAEd,KAAK,CAAC,EAAE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;IAIhC,UAAU,IAAI,IAAI;IAgD3B,gBAAgB,IAAI,MAAM,EAAE;CAQ7B"}