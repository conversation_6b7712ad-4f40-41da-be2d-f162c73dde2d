# 字段映射功能修改说明

## 修改概述

根据用户需求，已成功修改菜单栏中"导入字段匹配"功能的字段映射逻辑。当选择"试验问题表"作为字段映射数据表时，目标字段下拉框现在显示该表的完整字段列表，而不仅仅是问题管控字段。

## 修改内容

### 1. 主要修改
- **文件位置**: `src/views/dialogs.py`
- **修改方法**: `_get_problem_table_fields()`
- **修改范围**: 第2828-2944行

### 2. 功能变更

#### 修改前
- 目标字段下拉框仅显示"问题管控"分组的15个字段
- 字段来源：`config/problem_form_groups.json` 中的"问题管控"分组

#### 修改后
- 目标字段下拉框显示试验问题表的所有65个字段
- 字段来源：试验问题表的完整字段分组（单独列字段 + 固定字段 + 可变字段）

### 3. 新增方法

#### `_get_all_problem_table_fields()`
- 从问题表字段分组配置文件获取所有字段
- 支持多种配置文件格式的兼容性处理
- 自动合并单独列字段、固定字段、可变字段

#### `_get_problem_table_fields_fallback()`
- 提供默认的完整字段列表作为备用方案
- 包含65个试验问题表的所有字段
- 确保在配置文件缺失时功能正常

#### `_get_problem_table_fields_original()`
- 保留原始的问题管控字段获取逻辑
- 已注释说明保留原因
- 便于后续开发需要时恢复

## 字段范围对比

### 修改前（问题管控字段）
包含15个字段：
- 问题编号_QTM
- 问题进展详情
- 根本原因
- 问题根因类别
- 永久措施
- 问题状态
- 等...

### 修改后（所有字段）
包含65个字段，涵盖：

#### 单独列字段
- id、试验类型、样车编号、问题编号_QTM、问题编号_DTX
- 问题描述_QTM_DTS_DTM、图片名称、图片路径、纬度、经度
- 测试软件版本、问题日期_QTM_DTS_DTM、提出人等

#### 固定字段
- 市场TOP10问题_DTS、高感知相关字段、严重度_QTM_DTS_DTM
- 行车问题分类、泊车问题分类、接管类型、场景类型
- 功能模式、道路类型、问题发生位置等

#### 可变字段
- 平台迭代、智驾平台、项目名称_QTM、动力系统_QTM
- 问题来源_QTM、问题属性_QTM、故障部位_QTM等

## 代码保留策略

### 原始代码保留
- 原始的问题管控字段获取方法被完整保留
- 添加了详细的中文注释说明保留原因
- 方法名改为 `_get_problem_table_fields_original()`

### 保留原因
1. **后续开发需要**：可能需要恢复到仅显示问题管控字段的功能
2. **功能回滚**：如果新功能出现问题，可以快速回滚
3. **代码参考**：为其他类似功能提供参考实现

## 兼容性保证

### 向后兼容
- 保持现有的字段映射配置文件格式不变
- 原始记录表字段获取逻辑未受影响
- 所有现有功能正常工作

### 配置文件支持
- 支持新系统配置文件：`config/问题表字段分组.json`
- 支持旧系统配置文件：`config/问题表单独列字段.json` 等
- 自动降级到默认字段列表

## 测试验证

### 测试结果
- ✅ 字段数量从15个增加到65个
- ✅ 原有问题管控字段全部保留
- ✅ 新增字段正确显示
- ✅ 原始记录表功能正常
- ✅ 配置文件兼容性良好

### 验证项目
1. 试验问题表字段获取正确性
2. 问题管控字段包含性检查
3. 其他重要字段包含性检查
4. 原始记录表字段获取功能
5. 原始方法保留完整性

## 使用说明

### 操作步骤
1. 打开菜单栏 → 导入 → 导入字段匹配
2. 在"字段映射数据表"下拉框中选择"试验问题表"
3. 在目标字段下拉框中可以看到完整的65个字段选项
4. 配置字段映射关系并保存

### 预期效果
用户现在可以将Excel/CSV文件中的字段映射到试验问题表的任意字段，而不再局限于问题管控字段，大大提高了数据导入的灵活性和完整性。
