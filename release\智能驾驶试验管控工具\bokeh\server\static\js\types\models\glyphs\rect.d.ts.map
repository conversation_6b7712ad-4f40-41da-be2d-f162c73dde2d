{"version": 3, "file": "rect.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/models/glyphs/rect.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,eAAe,EAAE,mBAAmB,EAAE,mBAAmB,EAAC,MAAM,oBAAoB,CAAA;AAE5F,OAAO,EAAC,aAAa,EAAE,YAAY,EAAC,4BAAqB;AACzD,OAAO,EAAC,SAAS,EAAE,UAAU,EAAE,WAAW,EAAwB,yBAAkB;AACpF,OAAO,KAAK,KAAK,yBAAkB;AACnC,OAAO,KAAK,CAAC,8BAAuB;AAEpC,OAAO,EAAC,SAAS,EAAC,+BAAwB;AAC1C,OAAO,EAAC,SAAS,EAAC,MAAM,yBAAyB,CAAA;AACjD,OAAO,EAAC,KAAK,EAAC,MAAM,iBAAiB,CAAA;AAErC,oBAAY,QAAQ,GAAG,mBAAmB,GAAG;IAC3C,GAAG,EAAE,WAAW,CAAA;IAChB,GAAG,EAAE,WAAW,CAAA;IAChB,UAAU,EAAE,WAAW,CAAA;CACxB,CAAA;AAED,MAAM,WAAW,QAAS,SAAQ,QAAQ;CAAG;AAE7C,qBAAa,QAAS,SAAQ,mBAAmB;IACtC,KAAK,EAAE,IAAI,CAAA;IACX,OAAO,EAAE,IAAI,CAAC,OAAO,CAAA;IAKf,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC;cAU5B,SAAS,IAAI,IAAI;IA6BpC,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,EAAE,QAAQ,GAAG,IAAI;cAkCxD,SAAS,CAAC,QAAQ,EAAE,YAAY,GAAG,SAAS;cAI5C,UAAU,CAAC,QAAQ,EAAE,aAAa,GAAG,SAAS;IAwDjE,SAAS,CAAC,qCAAqC,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,WAAW,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EACxD,KAAK,EAAE,KAAK,GAAG,CAAC,WAAW,EAAE,WAAW,CAAC;IA+BzF,SAAS,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,KAAK,EAAE,UAAU,GAAG,UAAU;IAqBpE,qBAAqB,CAAC,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;CAGtF;AAED,yBAAiB,IAAI,CAAC;IACpB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,eAAe,CAAC,KAAK,GAAG;QAC1C,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;KAC5B,CAAA;IAED,KAAY,OAAO,GAAG,eAAe,CAAC,OAAO,CAAA;CAC9C;AAED,MAAM,WAAW,IAAK,SAAQ,IAAI,CAAC,KAAK;CAAG;AAE3C,qBAAa,IAAK,SAAQ,eAAe;IAC9B,UAAU,EAAE,IAAI,CAAC,KAAK,CAAA;IACtB,aAAa,EAAE,QAAQ,CAAA;gBAEpB,KAAK,CAAC,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;CAUxC"}