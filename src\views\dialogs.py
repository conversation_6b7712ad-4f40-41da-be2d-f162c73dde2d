from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QListWidget, QPushButton, QMessageBox, QListWidgetItem, QTableWidget, QTableWidgetItem, QRadioButton, QGroupBox, QFileDialog, QCheckBox, QAbstractItemView, QLineEdit, QInputDialog, QLabel, QFrame, QWidget, QTextEdit, QDialogButtonBox, QScrollArea, QHeaderView, QComboBox
from PyQt5.QtCore import Qt, QMimeData, pyqtSignal, QTimer
from PyQt5.QtGui import QDrag, QColor
import json # Added for template management
import os   # Added for path manipulation
import logging
import src.utils.form_config_manager as form_config_manager # Import the module
from src.utils.field_utils import filter_valid_fields

# 导入可搜索的ComboBox组件
try:
    from .custom_widgets import SearchableComboBox
except ImportError:
    # 如果导入失败，使用普通的QComboBox作为备用
    SearchableComboBox = QComboBox

# 导入TemplateEditDialog - 使用绝对导入避免与dialogs包冲突
try:
    # 直接使用绝对导入从src.dialogs模块导入
    # 注意：这里我们需要临时修改sys.path来确保能找到正确的模块
    import sys
    import os
    import importlib.util

    # 获取src目录的路径
    src_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    dialogs_file_path = os.path.join(src_dir, 'dialogs.py')

    # 使用importlib直接加载dialogs.py文件
    spec = importlib.util.spec_from_file_location("src_dialogs_module", dialogs_file_path)
    src_dialogs_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(src_dialogs_module)

    TemplateEditDialog = src_dialogs_module.TemplateEditDialog

except Exception as e:
    TemplateEditDialog = None
    print(f"Warning: TemplateEditDialog could not be imported: {e}")

try:
    # Assuming export_utils.py is in src/utils/
    from src.utils.export_utils import export_to_excel 
except ImportError as e:
    print(f"DEBUG: Failed to import export_to_excel: {e}")
    export_to_excel = None 
    print("Warning: export_to_excel could not be imported. Export will not work.")

from src.utils.form_config_manager import load_group_config, save_group_config

logger = logging.getLogger(__name__)

# 导入pandas用于Excel/CSV处理
try:
    import pandas as pd
except ImportError:
    pd = None

# 导入数据导入处理器
try:
    from src.utils.import_processor import ImportProcessor
except ImportError:
    ImportProcessor = None

# 修复作用域：将FIELD_PERMISSION_PATH定义到全局顶部
FIELD_PERMISSION_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config", "field_permission.json")

class ExportTableDialog(QDialog):
    def __init__(self, main_window, parent=None): # Added main_window parameter
        super().__init__(parent)
        self.main_window = main_window # Store main_window reference
        self.setWindowTitle("导出表格")

        # 🔧 优化：设置完整的窗口标志，确保显示所有标准窗口控制按钮
        # 包括最小化、最大化/还原、关闭按钮，与数据可视化分析窗口保持一致
        self.setWindowFlags(
            Qt.Dialog |                    # 对话框窗口
            Qt.WindowMinimizeButtonHint |  # 最小化按钮
            Qt.WindowMaximizeButtonHint |  # 最大化按钮
            Qt.WindowCloseButtonHint |     # 关闭按钮
            Qt.WindowSystemMenuHint        # 系统菜单（右键标题栏）
        )

        self.setMinimumSize(450, 350) # 设置一个最小尺寸

        # 主布局
        main_layout = QVBoxLayout(self)

        # 中部：模板列表
        self.template_list_widget = QListWidget()
        # 临时添加一些条目用于测试
        self.template_list_widget.addItems(["模板1", "模板2", "默认模板"])
        main_layout.addWidget(self.template_list_widget)

        # 底部：按钮水平布局
        button_layout = QHBoxLayout()
        self.new_template_button = QPushButton("新建模板")
        self.edit_template_button = QPushButton("修改模板")
        self.delete_template_button = QPushButton("删除模板")
        self.preview_table_button = QPushButton("表格预览")
        self.confirm_export_button = QPushButton("确认导出")

        button_layout.addWidget(self.new_template_button)
        button_layout.addWidget(self.edit_template_button)
        button_layout.addWidget(self.delete_template_button)
        button_layout.addStretch()
        button_layout.addWidget(self.preview_table_button)
        button_layout.addWidget(self.confirm_export_button)
        
        main_layout.addLayout(button_layout)
        self.setLayout(main_layout)

        # 连接信号 (目前是占位符)
        self.new_template_button.clicked.connect(self.open_new_template_dialog)
        self.edit_template_button.clicked.connect(self.open_edit_template_dialog)
        self.delete_template_button.clicked.connect(self.delete_template)
        self.preview_table_button.clicked.connect(self.on_preview_table)
        self.confirm_export_button.clicked.connect(self.on_confirm_export)

        self.load_saved_templates()

    def _get_template_file_path(self):
        pass # Temporarily remove docstring
        # self is src/views/dialogs.py
        # os.path.abspath(__file__) -> .../project/src/views/dialogs.py
        # os.path.dirname(os.path.abspath(__file__)) -> .../project/src/views
        # os.path.dirname(os.path.dirname(os.path.abspath(__file__))) -> .../project/src
        src_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        config_dir = os.path.join(src_dir, "config")
        return os.path.join(config_dir, "export_templates.json")

    def _load_all_templates_from_file(self):
        pass # Temporarily remove docstring
        file_path = self._get_template_file_path()
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    templates = json.load(f)
                    return templates if isinstance(templates, dict) else {}
            except json.JSONDecodeError:
                QMessageBox.warning(self, "加载错误", "模板文件格式错误或为空。") 
                return {}
            except Exception as e:
                QMessageBox.warning(self, "加载错误", "无法加载模板: " + str(e)) 
                return {}
        return {}

    def _save_all_templates_to_file(self, templates):
        pass # Temporarily remove docstring
        file_path = self._get_template_file_path()
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(templates, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            QMessageBox.critical(self, "保存错误", "无法保存模板: " + str(e)) 
            return False

    def load_saved_templates(self):
        pass # Temporarily remove docstring
        self.template_list_widget.clear()
        templates = self._load_all_templates_from_file()
        if templates:
            for template_name in templates.keys():
                self.template_list_widget.addItem(QListWidgetItem(template_name))
        # print(f"ExportTableDialog: Loaded templates: {list(templates.keys())}")

    def refresh_template_list(self):
        pass # Temporarily remove docstring
        self.load_saved_templates()

    def open_new_template_dialog(self):
        pass # Temporarily remove docstring
        if TemplateEditDialog is None:
            QMessageBox.critical(self, "错误", "TemplateEditDialog 未能加载，无法新建模板。")
            return
            
        dialog = TemplateEditDialog(main_window=self.main_window, parent=self)
        dialog.template_saved.connect(self.refresh_template_list)
        dialog.exec_()

    def open_edit_template_dialog(self):
        pass # Temporarily remove docstring
        current_item = self.template_list_widget.currentItem()
        if not current_item:
            QMessageBox.warning(self, "提示", "请先选择一个模板进行修改。")
            return
        
        if TemplateEditDialog is None:
            QMessageBox.critical(self, "错误", "TemplateEditDialog 未能加载，无法修改模板。")
            return

        template_name = current_item.text()
        templates = self._load_all_templates_from_file()
        template_data = templates.get(template_name)

        if template_data is None:
            message = "未找到模板 '" + template_name + "' 的数据。请尝试刷新列表。"
            QMessageBox.warning(self, "错误", message)
            return

        dialog = TemplateEditDialog(main_window=self.main_window, 
                                     template_data=template_data, 
                                     template_name=template_name, 
                                     parent=self)
        dialog.template_saved.connect(self.refresh_template_list)
        dialog.exec_()

    def delete_template(self):
        pass # Temporarily remove docstring
        current_item = self.template_list_widget.currentItem()
        if not current_item:
            QMessageBox.warning(self, "提示", "请先选择一个模板进行删除。")
            return

        template_name = current_item.text()
        question_message = "确定要删除模板 '" + template_name + "' 吗？此操作无法撤销。"
        reply = QMessageBox.question(self, '确认删除',
                                   question_message,
                                   QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            templates = self._load_all_templates_from_file()
            if template_name in templates:
                del templates[template_name]
                if self._save_all_templates_to_file(templates):
                    info_message = "模板 '" + template_name + "' 已被删除。"
                    QMessageBox.information(self, "删除成功", info_message)
                    self.refresh_template_list()
                else:
                    pass 
            else:
                warning_message = "模板 '" + template_name + "' 未在存储中找到，可能已被删除。"
                QMessageBox.warning(self, "错误", warning_message)
                self.refresh_template_list()

    def on_preview_table(self):
        # pass # Original placeholder, now implementing
        current_template_item = self.template_list_widget.currentItem()
        if not current_template_item:
            QMessageBox.warning(self, "提示", "请先选择一个模板进行预览。")
            return

        template_name = current_template_item.text()
        all_templates = self._load_all_templates_from_file()
        template_columns = all_templates.get(template_name)

        if not template_columns:
            QMessageBox.warning(self, "错误", f"无法加载模板 '{template_name}' 的列配置。")
            return
        
        # For preview, always get data for all rows (not just selected_rows_only=True)
        # The slicing to top 5 rows will happen after getting all data for the template columns.
        preview_headers, all_data_for_template = self._get_main_table_data(template_columns, selected_rows_only=False)

        if preview_headers is None: # Error handled in _get_main_table_data
            return
        
        preview_data_rows = all_data_for_template[:5] # Get top 5 rows

        if not preview_data_rows:
            QMessageBox.information(self, "预览为空", "根据模板和当前表格，没有可预览的数据行。")
            return

        # Assuming PreviewDataDialog is defined in the same file
        preview_dialog = PreviewDataDialog(preview_headers, preview_data_rows, self)
        preview_dialog.exec_()

    def on_confirm_export(self):
        # pass # Original placeholder, now implementing
        current_template_item = self.template_list_widget.currentItem()
        if not current_template_item:
            QMessageBox.warning(self, "提示", "请先选择一个模板进行导出。")
            return

        if export_to_excel is None:
            QMessageBox.critical(self, "错误", "导出功能未能加载 (export_to_excel)。请检查 utils/export_utils.py。")
            return

        template_name = current_template_item.text()
        all_templates = self._load_all_templates_from_file()
        template_columns = all_templates.get(template_name)

        if not template_columns:
            QMessageBox.warning(self, "错误", f"无法加载模板 '{template_name}' 的列配置。")
            return

        # Assuming ConfirmExportOptionsDialog is defined in the same file
        options_dialog = ConfirmExportOptionsDialog(
            export_table_dialog_ref=self, 
            template_columns=template_columns, 
            parent=self
        )
        if options_dialog.exec_() == QDialog.Accepted:
            export_type = options_dialog.get_export_option() # 'all', 'selected', or 'filtered'
            selected_only = (export_type == "selected")
            filtered_only = (export_type == "filtered")

            export_headers, data_to_export = self._get_main_table_data(template_columns, selected_rows_only=selected_only, filtered_rows_only=filtered_only)

            if export_headers is None: # Error handled in _get_main_table_data
                return 
            
            if not data_to_export:
                QMessageBox.information(self, "无数据导出", "根据您的选择和模板，没有数据可以导出。")
                return

            default_filename = f"{template_name}.xlsx"
            # Propose a save file dialog
            file_path, _ = QFileDialog.getSaveFileName(
                self, 
                "保存 Excel 文件", 
                default_filename, 
                "Excel 文件 (*.xlsx);;所有文件 (*)"
            )
            
            if file_path:
                success, message = export_to_excel(file_path, export_headers, data_to_export)
                if success:
                    QMessageBox.information(self, "导出成功", f"数据已成功导出到:\n{file_path}")
                else:
                    QMessageBox.critical(self, "导出失败", f"导出过程中发生错误: {message}")

    def _get_main_table_data(self, template_columns: list, selected_rows_only=False, filtered_rows_only=False):
        pass # Docstring temporarily removed
        if not hasattr(self.main_window, 'problem_table'):
            QMessageBox.critical(self, "错误", "主窗口中未找到问题表格 (problem_table)。")
            return None, None # Return a tuple for consistent unpacking

        main_table = self.main_window.problem_table
        main_headers = []
        for col_idx_main in range(main_table.columnCount()):
            header_item = main_table.horizontalHeaderItem(col_idx_main)
            if header_item:
                main_headers.append(header_item.text())
            else:
                main_headers.append(f"列 {col_idx_main}") 

        col_indices_to_export = []
        valid_template_headers_for_export = []
        for template_col_name in template_columns:
            try:
                idx_in_main_table = main_headers.index(template_col_name)
                col_indices_to_export.append(idx_in_main_table)
                valid_template_headers_for_export.append(template_col_name)
            except ValueError:
                print(f"警告: 模板列 '{template_col_name}' 在当前表格中未找到，将被忽略。")
                pass
        
        if not valid_template_headers_for_export: # Check against headers actually found
            QMessageBox.warning(self, "模板无效", "选定模板中的所有列均未在当前表格中找到或可用。")
            return None, None

        exported_data_rows = []
        for row_idx in range(main_table.rowCount()):
            # 检查行是否隐藏（筛选后未显示）
            if filtered_rows_only and main_table.isRowHidden(row_idx):
                continue
                
            if selected_rows_only:
                checkbox_container_widget = main_table.cellWidget(row_idx, 0) 
                checkbox_found_and_checked = False
                if checkbox_container_widget:
                    checkbox = checkbox_container_widget.findChild(QCheckBox)
                    if checkbox and checkbox.isChecked():
                        checkbox_found_and_checked = True
                if not checkbox_found_and_checked:
                    continue 
            
            current_row_data = []
            for col_idx_to_read in col_indices_to_export:
                item = main_table.item(row_idx, col_idx_to_read)
                current_row_data.append(item.text() if item else "")
            exported_data_rows.append(current_row_data)
        
        return valid_template_headers_for_export, exported_data_rows

class PreviewDataDialog(QDialog):
    """A dialog to display a preview of data in a QTableWidget."""
    def __init__(self, headers, data_rows, parent=None):
        super().__init__(parent)
        self.setWindowTitle("表格数据预览")
        self.setMinimumSize(600, 300)

        layout = QVBoxLayout(self)
        self.table_widget = QTableWidget()
        self.table_widget.setEditTriggers(QTableWidget.NoEditTriggers) # Read-only
        
        self.table_widget.setColumnCount(len(headers))
        self.table_widget.setHorizontalHeaderLabels(headers)
        self.table_widget.setRowCount(len(data_rows))

        for row_idx, row_data in enumerate(data_rows):
            for col_idx, cell_data in enumerate(row_data):
                self.table_widget.setItem(row_idx, col_idx, QTableWidgetItem(str(cell_data)))
        
        self.table_widget.resizeColumnsToContents()
        self.table_widget.resizeRowsToContents()
        
        layout.addWidget(self.table_widget)

        # Ok button to close
        self.ok_button = QPushButton("关闭")
        self.ok_button.clicked.connect(self.accept)
        layout.addWidget(self.ok_button, 0, Qt.AlignRight)

        self.setLayout(layout)

class ConfirmExportOptionsDialog(QDialog):
    """Dialog to get export options from the user (all rows vs. selected rows)."""
    def __init__(self, export_table_dialog_ref, template_columns, parent=None): # Added export_table_dialog_ref and template_columns
        super().__init__(parent)
        self.export_table_dialog_ref = export_table_dialog_ref # Store reference to ExportTableDialog
        self.template_columns = template_columns # Store current template columns

        self.setWindowTitle("确认导出选项")
        self.setMinimumWidth(350)
        # self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint) # Remove context help button if desired

        layout = QVBoxLayout(self)

        # Radio buttons for export type
        self.radio_all_rows = QRadioButton("导出所有行数据")
        self.radio_selected_rows = QRadioButton("仅导出复选框勾选中的行数据")
        self.radio_filtered_rows = QRadioButton("仅导出目前筛选的行数据")
        self.radio_all_rows.setChecked(True) # Default option

        radio_group_box = QGroupBox("选择导出范围")
        radio_layout = QVBoxLayout()
        radio_layout.addWidget(self.radio_all_rows)
        radio_layout.addWidget(self.radio_selected_rows)
        radio_layout.addWidget(self.radio_filtered_rows)
        radio_group_box.setLayout(radio_layout)
        layout.addWidget(radio_group_box)

        # Preview button - initialized here, will be added to button_box later
        self.preview_button = QPushButton("表格预览")
        self.preview_button.clicked.connect(self._preview_export_data)
        # layout.addWidget(self.preview_button) # Removed from here

        # Buttons for confirm/cancel
        button_box = QHBoxLayout()
        self.confirm_button = QPushButton("确认导出")
        self.confirm_button.clicked.connect(self.accept) # Dialog will be accepted
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)

        button_box.addStretch()
        button_box.addWidget(self.cancel_button)
        button_box.addWidget(self.preview_button) # Added preview button here
        button_box.addWidget(self.confirm_button)
        layout.addLayout(button_box)

        self.setLayout(layout)

    def get_export_option(self):
        """Returns 'all', 'selected', or 'filtered' based on radio button choice."""
        if self.radio_all_rows.isChecked():
            return "all"
        elif self.radio_selected_rows.isChecked():
            return "selected"
        else:
            return "filtered"

    def _preview_export_data(self):
        if not self.export_table_dialog_ref or not self.template_columns:
            QMessageBox.warning(self, "错误", "预览功能初始化不完整。")
            return

        export_type = self.get_export_option() # 'all', 'selected', or 'filtered'
        selected_only = (export_type == "selected")
        filtered_only = (export_type == "filtered")

        # Call the method from the ExportTableDialog instance
        preview_headers, all_data_for_template = self.export_table_dialog_ref._get_main_table_data(
            self.template_columns,
            selected_rows_only=selected_only,
            filtered_rows_only=filtered_only
        )

        if preview_headers is None: # Error message would have been shown by _get_main_table_data
            return
        
        preview_data_rows = all_data_for_template # Removed [:5] to show all data

        if not preview_data_rows:
            QMessageBox.information(self, "预览为空", "根据当前选择和模板，没有可预览的数据行。")
            return

        # PreviewDataDialog is defined in the same file
        # Parent is self (ConfirmExportOptionsDialog)
        preview_dialog = PreviewDataDialog(preview_headers, preview_data_rows, self)
        preview_dialog.exec_()

class DraggableListWidget(QListWidget):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setDragEnabled(True)
        self.setAcceptDrops(True)
        self.setDropIndicatorShown(True)
        self.setSelectionMode(QAbstractItemView.ExtendedSelection) # Allow multi-selection for dragging
        self.setDefaultDropAction(Qt.MoveAction)

    def startDrag(self, supportedActions):
        selected_items = self.selectedItems() 
        if not selected_items:
            return

        drag_data_payload = []
        for item in selected_items:
            payload_item = {
                "text": item.text(),
                "flags": int(item.flags())
            }
            if self.objectName() == "groups_list":
                payload_item["user_data"] = item.data(Qt.UserRole)
            drag_data_payload.append(payload_item)

        if not drag_data_payload:
            return

        mime_data = QMimeData()
        source_list_name = self.objectName() if self.objectName() else "unknown_source_list"
        
        combined_payload = {
            "source_list_name": source_list_name,
            "items": drag_data_payload
        }

        try:
            encoded_payload = json.dumps(combined_payload).encode('utf-8')
            mime_data.setData("application/x-custom-listwidget-item-transfer", encoded_payload)
        except (TypeError, json.JSONEncodeError) as e:
            logger.error(f"Error encoding drag data to JSON: {e}")
            return
            
        drag = QDrag(self)
        drag.setMimeData(mime_data)

        result_action = drag.exec_(supportedActions, Qt.MoveAction) 

        # Temporarily comment out the cleanup block to test if Qt handles removal for internal moves
        # or if this block was the source of the crash due to stale items.
        # if result_action == Qt.MoveAction:
        #     logger.debug(f"Drag from '{source_list_name}' resulted in MoveAction. (Original items would be removed here if not handled by dropEvent/Qt)")
        #     # for item_to_remove in selected_items: 
        #     #     row = self.row(item_to_remove) 
        #     #     if row != -1: 
        #     #         self.takeItem(row) 
        #     # logger.debug(f"Successfully removed items from source '{source_list_name}' after MoveAction (if loop was active).")
        # else:
        #     logger.debug(f"Drag from '{source_list_name}' did not result in MoveAction (action: {result_action}). Items not removed from source.")
        if result_action == Qt.MoveAction:
            logger.info(f"Drag from '{source_list_name}' resulted in MoveAction. Item removal from source is assumed to be handled by Qt or the drop event.")
        elif result_action != Qt.IgnoreAction: # IgnoreAction means drop was cancelled or no target
             logger.info(f"Drag from '{source_list_name}' resulted in action: {result_action}. Items not removed from source by startDrag.")

class GroupSettingsDialog(QDialog):
    # Signal to indicate settings have been saved
    settings_saved = pyqtSignal()

    def __init__(self, parent=None, main_window=None, all_field_names=None, current_groups=None):
        super().__init__(parent)
        self.main_window = main_window # Keep if needed for other things, or remove if only parent is needed
        self.parent_dock = parent # Assuming parent is the ProblemEditorDock
        
        # Store the provided list of all currently available field names
        self.all_available_field_names = all_field_names if all_field_names is not None else []
        # Store the initial group configuration passed from the dock
        self.initial_groups_config = current_groups if current_groups is not None else {}

        # self.config_manager = form_config_manager # Direct access to the module
        self.logger = logging.getLogger(__name__)

        # This will hold the QListWidgetItem for the currently selected group
        self.selected_group_item = None

        self.setWindowTitle("侧边栏分组设置")
        self.resize(1500, 1200) # Set initial size (width 1200, height 1200 from previous change)
        self.setModal(True)

        self._init_ui()
        self._load_all_fields_list()
        self._load_groups_list() # This will now use self.initial_groups_config
        self._update_field_availability()

        if self.groups_list_widget.count() > 0:
            self.groups_list_widget.setCurrentRow(0)

    def _init_ui(self):
        main_layout = QVBoxLayout(self)

        # Instructions
        instructions = QLabel("从左侧拖动表头到右侧分组内进行分配，可在分组内拖动排序。双击分组名可重命名。")
        main_layout.addWidget(instructions)

        # Lists layout
        lists_layout = QHBoxLayout()
        
        # All Fields (Available Fields)
        all_fields_container = QWidget()
        all_fields_v_layout = QVBoxLayout(all_fields_container)
        all_fields_v_layout.addWidget(QLabel("可用表头 (未分配):"))
        self.all_fields_list_widget = DraggableListWidget() 
        self.all_fields_list_widget.setObjectName("all_fields_list")
        all_fields_v_layout.addWidget(self.all_fields_list_widget)
        lists_layout.addWidget(all_fields_container)

        # Groups List
        groups_container = QWidget()
        groups_v_layout = QVBoxLayout(groups_container)
        groups_v_layout.addWidget(QLabel("侧边栏分组列表 (拖动排序):"))
        self.groups_list_widget = DraggableListWidget() # CHANGED from QListWidget
        self.groups_list_widget.setObjectName("groups_list")
        # self.groups_list.setDragDropMode(QAbstractItemView.InternalMove) # Enable internal reordering
        self.groups_list_widget.itemClicked.connect(self._on_group_selected)
        self.groups_list_widget.itemDoubleClicked.connect(self._rename_group)
        groups_v_layout.addWidget(self.groups_list_widget)
        lists_layout.addWidget(groups_container)

        # Group Fields List (Fields in Selected Group)
        group_fields_container = QWidget()
        group_fields_v_layout = QVBoxLayout(group_fields_container)
        group_fields_v_layout.addWidget(QLabel("当前分组内表头 (拖动排序):"))
        self.group_fields_list_widget = DraggableListWidget()
        self.group_fields_list_widget.setObjectName("group_fields_list")
        group_fields_v_layout.addWidget(self.group_fields_list_widget)
        lists_layout.addWidget(group_fields_container)
        
        main_layout.addLayout(lists_layout)

        # Buttons layout
        buttons_layout = QHBoxLayout()
        self.add_group_btn = QPushButton("新增分组")
        self.add_group_btn.clicked.connect(self._add_group)
        buttons_layout.addWidget(self.add_group_btn)

        self.delete_group_btn = QPushButton("删除分组")
        self.delete_group_btn.clicked.connect(self._delete_group)
        buttons_layout.addWidget(self.delete_group_btn)
        
        buttons_layout.addStretch()

        self.save_button = QPushButton("保存设置")
        self.save_button.clicked.connect(self._save_settings)
        buttons_layout.addWidget(self.save_button)

        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.cancel_button)

        main_layout.addLayout(buttons_layout)

        # Connect drop events
        self.all_fields_list_widget.dropEvent = self.drop_event_all_fields
        self.group_fields_list_widget.dropEvent = self.drop_event_group_fields
        self.groups_list_widget.dropEvent = self.drop_event_groups_list # ADDED for groups_list reordering

        self.all_fields_list_widget.dragEnterEvent = lambda event: self.drag_enter_event(event, self.all_fields_list_widget)
        self.group_fields_list_widget.dragEnterEvent = lambda event: self.drag_enter_event(event, self.group_fields_list_widget)
        self.groups_list_widget.dragEnterEvent = lambda event: self.drag_enter_event(event, self.groups_list_widget) # ADDED
        
        self.all_fields_list_widget.dragMoveEvent = lambda event: event.accept()
        self.group_fields_list_widget.dragMoveEvent = lambda event: event.accept()
        self.groups_list_widget.dragMoveEvent = lambda event: event.accept() # ADDED

    def drag_enter_event(self, event, target_list_widget):
        if event.mimeData().hasFormat("application/x-custom-listwidget-item-transfer"):
            event.acceptProposedAction()
        else:
            event.ignore()

    def _get_dragged_data(self, event):
        """Decodes the MIME data from a drag operation.

        Returns:
            tuple: (source_list_name, list_of_item_dicts) or (None, None) on error.
                   Each item_dict is {"text": str, "flags": Qt.ItemFlags}.
        """
        mime_format = "application/x-custom-listwidget-item-transfer"
        if not event.mimeData().hasFormat(mime_format):
            logger.warning(f"MIME data does not have expected format: {mime_format}")
            return None, None

        try:
            byte_data = event.mimeData().data(mime_format)
            if byte_data is None or not hasattr(byte_data, 'data'):
                logger.error("MIME data for format is None or not a QByteArray.")
                return None, None
            
            mime_data_str = byte_data.data().decode('utf-8')
            
            combined_payload = json.loads(mime_data_str)
            source_list_name = combined_payload.get("source_list_name")
            items_data = combined_payload.get("items")

            if not source_list_name or items_data is None or not isinstance(items_data, list):
                logger.error(f"Invalid drag payload structure: {combined_payload}")
                return None, None

            processed_items = []
            for item_dict in items_data:
                if isinstance(item_dict, dict) and "text" in item_dict and "flags" in item_dict:
                    try:
                        flags = Qt.ItemFlags(int(item_dict["flags"]))
                        processed_item = {"text": item_dict["text"], "flags": flags}
                        # Check for and include user_data if present (for group items)
                        if "user_data" in item_dict:
                            processed_item["user_data"] = item_dict["user_data"]
                        processed_items.append(processed_item)
                    except ValueError:
                        logger.error(f"Could not convert item_flags '{item_dict['flags']}' to int for item '{item_dict['text']}'. Using NoItemFlags.")
                        processed_items.append({"text": item_dict["text"], "flags": Qt.NoItemFlags})
                else:
                    logger.warning(f"Skipping malformed item_dict in drag data: {item_dict}")
            
            return source_list_name, processed_items
            
        except json.JSONDecodeError as e:
            logger.error(f"Error decoding JSON from MIME data: {e}. Data: '{mime_data_str if 'mime_data_str' in locals() else '<unknown>'}'")
            return None, None
        except Exception as e:
            logger.error(f"General error decoding MIME data: {e}")
            return None, None

    def drop_event_all_fields(self, event):
        """Handles drop events on the all_fields_list (unassigning fields)."""
        if event.mimeData().hasFormat("application/x-custom-listwidget-item-transfer"):
            source_list_name, item_dicts = self._get_dragged_data(event)
            if source_list_name is None or item_dicts is None:
                event.ignore()
                return

            if source_list_name == "group_fields_list": 
                added_items_texts_to_all_fields = []
                for item_dict in item_dicts:
                    item_text = item_dict["text"]
                    exists = False
                    for i in range(self.all_fields_list_widget.count()):
                        if self.all_fields_list_widget.item(i).text() == item_text:
                            exists = True
                            break
                    if not exists:
                        new_item = QListWidgetItem(item_text)
                        new_item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsDragEnabled)
                        self.all_fields_list_widget.addItem(new_item)
                        added_items_texts_to_all_fields.append(item_text)
                
                if added_items_texts_to_all_fields:
                    self.all_fields_list_widget.sortItems()
                    logger.debug(f"Items {added_items_texts_to_all_fields} visually added to all_fields_list from group_fields_list.")

                source_group_item = self.groups_list_widget.currentItem()
                if source_group_item:
                    fields_in_source_group = source_group_item.data(Qt.UserRole)
                    if fields_in_source_group is None: fields_in_source_group = []
                    dragged_texts_from_group = [d['text'] for d in item_dicts]
                    updated_fields_for_source_group = [f for f in fields_in_source_group if f not in dragged_texts_from_group]
                    if updated_fields_for_source_group != fields_in_source_group:
                        source_group_item.setData(Qt.UserRole, updated_fields_for_source_group)
                        logger.debug(f"UserRole for source group '{source_group_item.text()}' updated. Removed: {dragged_texts_from_group}.")
                
                event.acceptProposedAction() 
                
                # Explicitly refresh the source group's field list display
                if event.isAccepted() and source_group_item:
                    # This call will use the updated UserRole to refresh group_fields_list_widget
                    self._on_group_selected(source_group_item)
                
                self._update_field_availability() 
            else:
                logger.debug(f"Drop on all_fields_list ignored: source was '{source_list_name}', not group_fields_list.")
                event.ignore()
        else:
            logger.debug("Drop on all_fields_list ignored: wrong MIME type.")
            event.ignore()

    def drop_event_group_fields(self, event):
        """Handles drop events on the group_fields_list (assigning or reordering)."""
        target_group_item = self.groups_list_widget.currentItem()
        if not target_group_item:
            logger.warning("Drop on group_fields_list but no group selected.")
            event.ignore()
            return

        if event.mimeData().hasFormat("application/x-custom-listwidget-item-transfer"):
            source_list_name, item_dicts = self._get_dragged_data(event)
            if source_list_name is None or item_dicts is None:
                event.ignore()
                return

            target_widget = self.group_fields_list_widget

            if source_list_name == "all_fields_list": # Assigning new fields from all_fields_list
                dropped_on_item_widget = target_widget.itemAt(event.pos())
                insert_row = target_widget.row(dropped_on_item_widget) if dropped_on_item_widget else target_widget.count()
                added_items_texts = []

                items_to_remove_from_source = []

                for item_dict in reversed(item_dicts): # Iterate reversed to maintain order when inserting at same index
                    item_text = item_dict["text"]
                    item_flags = Qt.ItemIsEnabled | Qt.ItemIsSelectable | Qt.ItemIsDragEnabled
                    
                    already_in_group = False
                    for i in range(target_widget.count()):
                        if target_widget.item(i).text() == item_text:
                            already_in_group = True
                            break
                    if not already_in_group:
                        new_item = QListWidgetItem(item_text)
                        new_item.setFlags(item_flags)
                        target_widget.insertItem(insert_row, new_item)
                        added_items_texts.append(item_text)
                        items_to_remove_from_source.append(item_text) # Mark for removal from source
                    else:
                        logger.debug(f"Item '{item_text}' from all_fields_list already in group. Skipped.")
                
                if added_items_texts:
                    logger.debug(f"Items {added_items_texts} added to group '{target_group_item.text()}' from all_fields_list.")
                    event.acceptProposedAction()
                    if event.isAccepted(): # If drop was successful, remove from source
                        for text_to_remove in items_to_remove_from_source:
                             for i in range(self.all_fields_list_widget.count() - 1, -1, -1):
                                if self.all_fields_list_widget.item(i).text() == text_to_remove:
                                    self.all_fields_list_widget.takeItem(i)
                                    break
                else:
                    event.ignore()

            elif source_list_name == target_widget.objectName(): # REORDERING within group_fields_list
                logger.debug(f"Reordering in group_fields_list for group '{target_group_item.text()}'.")
                
                dragged_items_data = []
                original_rows_taken = []

                # Phase 1: Collect data and remove original items from their current positions
                # We must iterate based on selectedItems before they are modified by takeItem
                # This is tricky because item_dicts is from selectedItems, but indices change.
                # A common strategy: take items out, then re-insert.
                
                # Take out all dragged items first, storing their data.
                # Iterate based on the texts from item_dicts, assuming these are the selected items.
                # This requires texts to be unique within the selection for simple text-based removal.
                # A more robust approach would be to get QListWidgetItems in startDrag, but that's complex with MimeData.

                # Let's try a "take and reinsert" approach. Store all items, clear, re-add.
                # No, this is too disruptive visually.
                #
                # We need to remove the *actual* selected items.
                # The item_dicts contains the TEXTS of the selected items.
                # We can find these items by text and remove them.
                # However, if multiple items have the same text, this is ambiguous.
                # For now, assume field names within a group are unique for reordering.

                # Store data of items being dragged
                for item_d in item_dicts:
                    dragged_items_data.append({"text": item_d["text"], "flags": item_d["flags"]})

                # Remove these items from the list
                # Iterate backwards to preserve indices during removal if removing by index
                indices_to_remove = []
                # Find rows of items to be dragged (this part assumes a previous implementation detail)
                # Let's rely on removing by text for now, assuming uniqueness in group_fields for simplicity of this step
                # If not unique, this part will be buggy.
                
                # To make this robust: use the positions of the items being dragged
                # This is harder as item_dicts only has text/flags.
                # For simplicity, we will assume that Qt's MoveAction *should* have removed them if the drag originated from here.
                # If it didn't, and we insert new ones, we get duplicates.
                # The previous attempt to "rely on Qt" for internal moves failed (user reported "cannot drag").
                # So we MUST manually manage.

                # Create a list of (row, item_data) for items to be moved, then remove them.
                items_to_move_with_rows = []
                all_current_items_data = []
                for i in range(target_widget.count()):
                    item = target_widget.item(i)
                    all_current_items_data.append({"text": item.text(), "flags": item.flags(), "original_row": i})
                
                # Identify which of these are in item_dicts
                # This is inefficient but necessary if we only have text from item_dicts
                temp_item_dicts_texts = [d["text"] for d in item_dicts]
                
                for i in range(target_widget.count() -1, -1, -1): # Iterate backwards for removal
                    item = target_widget.item(i)
                    if item.text() in temp_item_dicts_texts:
                        # If allowing duplicate text in selection, this needs adjustment
                        # For now, assume we remove first match found
                        items_to_move_with_rows.append({"text": item.text(), "flags": item.flags()})
                        target_widget.takeItem(i)
                        temp_item_dicts_texts.remove(item.text()) # Consume the text match

                # Reverse items_to_move_with_rows because we took them in reverse order
                items_to_move_with_rows.reverse() 
                
                # Determine insert position
                dropped_on_item_widget = target_widget.itemAt(event.pos())
                insert_row = target_widget.row(dropped_on_item_widget) if dropped_on_item_widget else target_widget.count()

                # Insert items at the new position
                for item_data in items_to_move_with_rows: # These are the items we just took out
                    new_item = QListWidgetItem(item_data["text"])
                    new_item.setFlags(item_data["flags"])
                    target_widget.insertItem(insert_row, new_item)
                    insert_row += 1 # Increment for subsequent items in multi-drag
                
                event.acceptProposedAction()
            else:
                logger.debug(f"Drop on group_fields_list from unexpected source '{source_list_name}'. Ignored.")
                event.ignore()
            
            if event.isAccepted():
                self._update_current_group_item_data_from_ui() 
                self._update_field_availability()
        else:
            logger.debug("Drop on group_fields_list ignored: wrong MIME type.")
            event.ignore()

    def drop_event_groups_list(self, event):
        """Handles drop events on the groups_list for reordering group items."""
        target_widget = self.groups_list_widget
        if not event.mimeData().hasFormat("application/x-custom-listwidget-item-transfer"):
            logger.debug("Drop on groups_list ignored: wrong MIME type.")
            event.ignore()
            return

        source_list_name, item_dicts = self._get_dragged_data(event)

        if source_list_name is None or item_dicts is None:
            logger.error("Could not get dragged data for groups_list drop.")
            event.ignore()
            return

        if source_list_name != target_widget.objectName():
            logger.debug(f"Drop on groups_list ignored: source was '{source_list_name}', not groups_list itself.")
            event.ignore()
            return

        logger.debug("Reordering in groups_list.")
        
        # Store data of items being dragged (including UserRole for groups)
        dragged_items_data = []
        for item_d in item_dicts:
            dragged_items_data.append({
                "text": item_d["text"], 
                "flags": item_d["flags"],
                "user_data": item_d.get("user_data") 
            })

        # Remove these items from the list by text. Group names should be unique.
        temp_item_dicts_texts = [d["text"] for d in item_dicts]
        for i in range(target_widget.count() - 1, -1, -1):
            item = target_widget.item(i)
            if item.text() in temp_item_dicts_texts:
                target_widget.takeItem(i)
                temp_item_dicts_texts.remove(item.text()) # Consume
        
        # Determine insert position
        dropped_on_item_widget = target_widget.itemAt(event.pos())
        insert_row = target_widget.row(dropped_on_item_widget) if dropped_on_item_widget else target_widget.count()

        # Insert items at the new position
        for item_data in dragged_items_data: # Items in original drag order
            new_item = QListWidgetItem(item_data["text"])
            new_item.setFlags(item_data["flags"])
            if item_data["user_data"] is not None:
                new_item.setData(Qt.UserRole, item_data["user_data"])
            target_widget.insertItem(insert_row, new_item)
            insert_row += 1
            
        event.acceptProposedAction()
        
        if event.isAccepted():
            # If a group was moved, its selection might change or fields might need refresh
            # if the selected group was part of the move.
            # For simplicity, if any group is selected, refresh its fields.
            current_sel_group = target_widget.currentItem()
            if current_sel_group:
                self._on_group_selected(current_sel_group)
            self._update_field_availability() # This is important too

    def _update_current_group_item_data_from_ui(self):
        """Immediately updates the UserRole data of the current group item from group_fields_list."""
        current_group_item = self.groups_list_widget.currentItem()
        if current_group_item:
            current_fields_in_list = []
            for i in range(self.group_fields_list_widget.count()):
                current_fields_in_list.append(self.group_fields_list_widget.item(i).text())
            current_group_item.setData(Qt.UserRole, current_fields_in_list)
            logger.debug(f"Updated UserRole for group '{current_group_item.text()}' with: {current_fields_in_list}")

    def _load_all_fields_list(self):
        """Populates the list of all available fields from self.all_available_field_names."""
        self.all_fields_list_widget.clear()
        # self.logger.debug(f"GroupSettingsDialog: Populating all_fields_list_widget with: {self.all_available_field_names}")
        for field_name in sorted(list(set(self.all_available_field_names))): # Ensure unique and sorted
            item = QListWidgetItem(field_name)
            self.all_fields_list_widget.addItem(item)

    def _load_groups_list(self):
        """Loads groups into the groups list. Uses self.initial_groups_config if provided."""
        self.groups_list_widget.clear()
        self.group_fields_list_widget.clear() # Clear fields of any previously selected group

        groups_to_load = self.initial_groups_config
        # self.logger.debug(f"GroupSettingsDialog: Loading groups. Initial config: {groups_to_load}")

        if not groups_to_load: # If initial_groups_config is empty or None
            # self.logger.info("No initial group config provided or it was empty. Will check file or start fresh.")
            # Fallback to loading from file if necessary, or start with an empty group list
            # For now, if initial_groups_config is empty, we assume it means no groups should be pre-loaded
            # unless ProblemEditorDock already created a default one and passed it.
            pass # UI will remain empty or user can add new groups

        for group_name, fields in groups_to_load.items():
            group_item = QListWidgetItem(group_name)
            # Store the fields directly in the item's data. Ensure it's a list.
            group_item.setData(Qt.UserRole, fields if isinstance(fields, list) else []) 
            self.groups_list_widget.addItem(group_item)
        
        # self.logger.debug(f"GroupSettingsDialog: Groups loaded into list widget. Count: {self.groups_list_widget.count()}")

    def _update_field_availability(self):
        """Updates the list of unassigned fields based on all_available_field_names and fields in current groups."""
        # self.logger.debug("GroupSettingsDialog: Updating field availability.")
        assigned_fields = set()
        for i in range(self.groups_list_widget.count()):
            group_item = self.groups_list_widget.item(i)
            fields_in_group = group_item.data(Qt.UserRole) # This is a list of field names
            if fields_in_group and isinstance(fields_in_group, list):
                assigned_fields.update(fields_in_group)
        
        # self.logger.debug(f"Fields assigned in groups: {assigned_fields}")

        self.all_fields_list_widget.clear()
        # Populate with fields from self.all_available_field_names that are not in assigned_fields
        unassigned_fields = sorted(list(set(self.all_available_field_names) - assigned_fields))
        # self.logger.debug(f"Unassigned fields to display: {unassigned_fields}")

        for field_name in unassigned_fields:
            item = QListWidgetItem(field_name)
            self.all_fields_list_widget.addItem(item)
        
        # self.logger.debug(f"all_fields_list_widget count after update: {self.all_fields_list_widget.count()}")

    def _on_group_selected(self, group_item):
        if not group_item: 
            self.group_fields_list_widget.clear()
            return
        
        self.group_fields_list_widget.clear()
        fields_for_group = self._get_fields_for_group_item(group_item)

        for field_name in fields_for_group:
            # Ensure field is valid before adding
            if field_name in self.all_available_field_names:
                item = QListWidgetItem(field_name)
                self.group_fields_list_widget.addItem(item)
            else:
                logger.warning(f"Field '{field_name}' from group '{group_item.text()}' is no longer in all_available_field_names. Skipping.")
        self._update_field_availability() # Update availability when group selection changes

    def _add_group(self):
        group_name, ok = QInputDialog.getText(self, "新增分组", "请输入新分组名称:", QLineEdit.Normal, f"新分组_{self.groups_list_widget.count() + 1}")
        if ok and group_name:
            # Check for duplicate group name
            for i in range(self.groups_list_widget.count()):
                if self.groups_list_widget.item(i).text() == group_name:
                    QMessageBox.warning(self, "名称重复", "该分组名称已存在。")
                    return
            item = QListWidgetItem(group_name)
            item.setData(Qt.UserRole, []) # New group starts with empty fields
            self.groups_list_widget.addItem(item)
            self.groups_list_widget.setCurrentItem(item)
            self._on_group_selected(item)

    def _delete_group(self):
        current_item = self.groups_list_widget.currentItem()
        if not current_item:
            QMessageBox.warning(self, "提示", "请选择要删除的分组。")
            return

        reply = QMessageBox.question(self, "确认删除", f"确定要删除分组 '{current_item.text()}' 吗？\n该分组内的表头将变为未分配状态。",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        if reply == QMessageBox.Yes:
            # Fields in this group are effectively returned to all_fields_list by not being in any group
            self.groups_list_widget.takeItem(self.groups_list_widget.row(current_item))
            self.group_fields_list_widget.clear()
            if self.groups_list_widget.count() > 0:
                self.groups_list_widget.setCurrentRow(0)
                self._on_group_selected(self.groups_list_widget.currentItem())
            self._update_field_availability()

    def _rename_group(self, item):
        old_name = item.text()
        new_name, ok = QInputDialog.getText(self, "重命名分组", "请输入新的分组名称:", QLineEdit.Normal, old_name)
        if ok and new_name and new_name != old_name:
            # Check for duplicate group name
            for i in range(self.groups_list_widget.count()):
                if self.groups_list_widget.item(i).text() == new_name and self.groups_list_widget.item(i) != item:
                    QMessageBox.warning(self, "名称重复", "该分组名称已存在。")
                    return
            item.setText(new_name)

    def _collect_current_config_from_ui(self):
        """ Collects the current group configuration from the UI elements. """
        updated_groups = {}
        for i in range(self.groups_list_widget.count()):
            group_item = self.groups_list_widget.item(i)
            group_name = group_item.text()
            # Fields for this group are stored in its UserRole data
            fields_in_group = group_item.data(Qt.UserRole)
            if fields_in_group is None: # Should ideally be an empty list if no fields
                fields_in_group = []
            updated_groups[group_name] = fields_in_group
        # self.logger.debug(f"GroupSettingsDialog: Collected config from UI: {updated_groups}")
        return updated_groups

    def _save_settings(self):
        """Saves the current group configuration from the UI to the config file."""
        # Make sure to update the data of the currently selected group from its field list widget
        # before collecting all configurations.
        if self.groups_list_widget.currentItem():
            self._update_current_group_item_data_from_ui()

        current_ui_config = self._collect_current_config_from_ui()
        self.logger.info(f"Saving group settings: {current_ui_config}")
        
        # The GroupSettingsDialog should not save directly. It should return the config.
        # The responsibility of saving (e.g., to file) should be with ProblemEditorDock
        # or a dedicated config manager called by ProblemEditorDock after the dialog is accepted.
        # For now, to align with ProblemEditorDock's expectation, this method will not save to file.
        # self.config_manager.save_group_config(current_ui_config)
        
        # Instead of saving to file here, we store it to be retrieved by get_updated_groups
        self.updated_groups_config = current_ui_config
        self.accept() # Close the dialog with QDialog.Accepted

    def get_updated_groups(self):
        """Called by ProblemEditorDock after the dialog is accepted to get the new configuration."""
        return getattr(self, 'updated_groups_config', {}) # Return the config collected from UI

    def _get_fields_for_group_item(self, group_item):
        """Helper to get fields from a group list item, robustly."""
        if not group_item: return []
        # Try to get from UserRole first (populated by _load_groups_list or updated by _save_settings)
        fields = group_item.data(Qt.UserRole)
        if fields is not None: return fields
        
        # Fallback: find in self.initial_groups_config by name (if UserRole wasn't set/updated yet)
        group_name_from_item = group_item.text()
        for g_cfg in self.initial_groups_config.get("groups", []):
            if g_cfg.get("name") == group_name_from_item:
                return g_cfg.get("fields", [])
        return []

class ResizableConfirmationDialog(QDialog):
    def __init__(self, title, message, parent=None):
        super().__init__(parent)
        self.setWindowTitle(title)
        # QDialogs are resizable by default. We can set a minimum size.
        self.setMinimumSize(600, 600) 

        main_layout = QVBoxLayout(self)

        # Use QTextEdit for better text handling, scrollability, and selection
        self.message_text_edit = QTextEdit()
        self.message_text_edit.setPlainText(message)
        self.message_text_edit.setReadOnly(True)
        # Optional: Adjust text interaction flags if needed
        # self.message_text_edit.setTextInteractionFlags(Qt.TextSelectableByMouse | Qt.TextSelectableByKeyboard)
        main_layout.addWidget(self.message_text_edit)

        # Buttons layout
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch() # Push buttons to the right

        self.yes_button = QPushButton("是")
        self.yes_button.clicked.connect(self.accept)
        buttons_layout.addWidget(self.yes_button)

        self.no_button = QPushButton("否")
        self.no_button.clicked.connect(self.reject)
        buttons_layout.addWidget(self.no_button)
        
        main_layout.addLayout(buttons_layout)
        self.setLayout(main_layout)

class ManageTemplatesDialog(QDialog):
    def __init__(self, mode, problem_editor_dock, parent=None):
        super().__init__(parent)
        self.mode = mode # 'call' or 'delete'
        self.problem_editor_dock = problem_editor_dock # Reference to ProblemEditorDock
        self.logger = logging.getLogger(__name__)

        if self.mode == 'call':
            self.setWindowTitle("调用问题模板")
        elif self.mode == 'delete':
            self.setWindowTitle("删除问题模板")
        else:
            self.setWindowTitle("管理模板") # Fallback
            self.logger.warning(f"ManageTemplatesDialog initialized with invalid mode: {self.mode}")

        self.setMinimumSize(800, 400)  # 增加窗口最小尺寸以适应横向布局

        # 🔧 优化：设置完整的窗口标志，确保显示所有标准窗口控制按钮
        # 包括最小化、最大化/还原、关闭按钮，与其他对话框保持一致
        from src.utils.window_utils import get_standard_dialog_flags
        self.setWindowFlags(get_standard_dialog_flags())

        self._init_ui()
        self._load_templates()

    def _init_ui(self):
        self.setWindowTitle("模板管理")
        self.setMinimumSize(800, 400)
        main_layout = QVBoxLayout(self)
        content_layout = QHBoxLayout()
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(0, 0, 0, 0)
        self.templates_list_widget = QListWidget()
        self.templates_list_widget.setMinimumWidth(200)
        self.templates_list_widget.itemClicked.connect(self._on_template_selected)
        left_layout.addWidget(self.templates_list_widget)
        right_widget = QWidget()
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)
        self.template_content = QTextEdit()
        self.template_content.setReadOnly(True)
        right_layout.addWidget(self.template_content)
        content_layout.addWidget(left_widget)
        content_layout.addWidget(right_widget)
        main_layout.addLayout(content_layout)
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        # 先全部设为None
        self.confirm_button = None
        self.delete_button = None
        self.cancel_button = None
        if self.mode == "call":
            self.confirm_button = QPushButton("确认调用")
            self.confirm_button.clicked.connect(self._confirm_action)
            button_layout.addWidget(self.confirm_button)
        elif self.mode == "delete":
            self.delete_button = QPushButton("删除选中模板")
            self.delete_button.clicked.connect(self._delete_selected_template_action)
            button_layout.addWidget(self.delete_button)
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)
        main_layout.addLayout(button_layout)
        self._load_templates()

    def _on_template_selected(self, item):
        """当模板被选中时，在右侧显示模板内容"""
        template_name = item.text()
        templates_config = form_config_manager.load_templates()
        template_data = None
        for tpl in templates_config.get("templates", []):
            if tpl.get("name") == template_name:
                template_data = tpl.get("data")
                break
        
        if template_data:
            # 将模板数据格式化为JSON字符串并显示
            formatted_json = json.dumps(template_data, ensure_ascii=False, indent=2)
            self.template_content.setText(formatted_json)
        else:
            self.template_content.clear()

    def _load_templates(self):
        self.templates_list_widget.clear()
        templates_config = form_config_manager.load_templates()
        template_list = templates_config.get("templates", [])
        if not template_list:
            self.templates_list_widget.addItem("没有可用的模板")
            self.templates_list_widget.setEnabled(False)
            if self.confirm_button:
                self.confirm_button.setEnabled(False)
            if self.delete_button:
                self.delete_button.setEnabled(False)
        else:
            self.templates_list_widget.setEnabled(True)
            for tpl in template_list:
                self.templates_list_widget.addItem(QListWidgetItem(tpl.get("name")))
            if self.confirm_button:
                self.confirm_button.setEnabled(self.mode == 'call')
            if self.delete_button:
                self.delete_button.setEnabled(self.mode == 'delete')

    def _confirm_action(self):
        if self.mode == 'call':
            self._apply_selected_template()
        # No confirm action for delete mode directly on this button, it's handled by delete_button

    def _apply_selected_template(self):
        selected_items = self.templates_list_widget.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "未选择", "请先选择一个模板进行调用。")
            return
        template_name = selected_items[0].text()
        templates_config = form_config_manager.load_templates()
        template_to_apply = None
        for tpl in templates_config.get("templates", []):
            if tpl.get("name") == template_name:
                template_to_apply = tpl.get("data")
                break
        if not template_to_apply or not self.problem_editor_dock:
            QMessageBox.critical(self, "错误", f"无法加载或应用模板 '{template_name}'。")
            return
        # 获取当前表格中的所有列名，排除"全不选"
        if hasattr(self.problem_editor_dock.parent_window, 'problem_table'):
            table = self.problem_editor_dock.parent_window.problem_table
            current_columns = set(table.horizontalHeaderItem(i).text() for i in range(table.columnCount()) if table.horizontalHeaderItem(i).text() != "全不选")
        else:
            current_columns = set()
        template_columns = set(k for k in template_to_apply.keys() if k != "全不选")
        missing_columns = template_columns - current_columns
        extra_columns = current_columns - template_columns
        if missing_columns or extra_columns:
            diff_info = []
            if missing_columns:
                missing_text = "\n".join([f"• {col}" for col in sorted(missing_columns)])
                diff_info.append(f"模板中存在但当前表格中不存在的列：\n{missing_text}")
            if extra_columns:
                extra_text = "\n".join([f"• {col}" for col in sorted(extra_columns)])
                diff_info.append(f"当前表格中存在但模板中不存在的列：\n{extra_text}")
            diff_message = "\n\n".join(diff_info)
            QMessageBox.warning(self, "模板差异警告", f"检测到模板与当前表格存在列差异：\n\n{diff_message}\n\n建议重新保存模板以更新列信息。")
            return
        # 其余调用逻辑保持不变
        self.problem_editor_dock.apply_template(template_to_apply)
        self.accept()

    def _delete_selected_template_action(self):
        selected_items = self.templates_list_widget.selectedItems()
        if not selected_items:
            QMessageBox.warning(self, "未选择", "请先选择一个模板进行删除。")
            return

        template_name = selected_items[0].text()
        reply = QMessageBox.question(self, "确认删除", 
                                     f"确定要删除模板 '{template_name}' 吗？此操作无法撤销。",
                                     QMessageBox.Yes | QMessageBox.No, QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            if form_config_manager.delete_template(template_name):
                self.logger.info(f"Template '{template_name}' deleted.")
                self._load_templates() # Refresh the list
                QMessageBox.information(self, "成功", f"模板 '{template_name}' 已删除。")
            else:
                QMessageBox.warning(self, "失败", f"删除模板 '{template_name}' 失败。")

class FieldImportDialog(QDialog):
    """问题表字段导入设置窗口"""
    fields_imported = pyqtSignal(dict)  # 信号：当字段导入完成时发出

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("问题表字段导入设置")
        self.setMinimumWidth(1000)  # 增加最小宽度
        self.setMinimumHeight(700)  # 增加最小高度
        
        # 存储JSON文件路径和数据
        self.json_file_paths = {}  # 初始化为空字典
        self.json_data = {}
        self.current_json_type = None
        
        # 添加配置文件路径 - 统一使用config目录
        self.config_file_path = os.path.join(os.getcwd(), 'config', 'field_import_paths.json')
        
        # 添加延迟保存定时器
        self.save_timer = QTimer()
        self.save_timer.setSingleShot(True)  # 设置为单次触发
        self.save_timer.timeout.connect(self._delayed_save)
        self.pending_save_type = None  # 记录待保存的JSON类型
        
        self._init_ui()
        self._load_saved_paths()  # 加载保存的路径
        
    def _init_ui(self):
        layout = QVBoxLayout()
        
        # 上部：文件路径加载区域
        file_group = QGroupBox("JSON文件路径")
        file_layout = QVBoxLayout()
        
        # 为每种JSON类型创建文件路径输入框和浏览按钮
        for json_type in ["单独列字段", "固定字段", "可变字段"]:
            h_layout = QHBoxLayout()
            label = QLabel(f"{json_type}JSON:")
            self.json_file_paths[json_type] = QLineEdit()  # 在这里初始化QLineEdit
            self.json_file_paths[json_type].setReadOnly(True)
            browse_btn = QPushButton("浏览")
            browse_btn.clicked.connect(lambda checked, t=json_type: self.on_browse_json_file(t))
            
            h_layout.addWidget(label)
            h_layout.addWidget(self.json_file_paths[json_type])
            h_layout.addWidget(browse_btn)
            file_layout.addLayout(h_layout)
            
        file_group.setLayout(file_layout)
        layout.addWidget(file_group)
        
        # 下部：文件列表、工具栏和表格区域
        bottom_layout = QHBoxLayout()
        
        # 左侧：JSON文件列表
        self.json_file_list = QListWidget()
        self.json_file_list.setMaximumWidth(150)  # 限制列表宽度
        self.json_file_list.itemClicked.connect(self.on_json_file_list_item_clicked)
        bottom_layout.addWidget(self.json_file_list)
        
        # 中间：工具栏
        toolbar_layout = QVBoxLayout()
        toolbar_layout.setSpacing(5)  # 设置按钮间距
        
        # 创建工具栏按钮组
        button_group = QGroupBox("键值对操作")
        button_layout = QVBoxLayout()
        button_layout.setSpacing(5)
        
        self.new_row_btn = QPushButton("新增行")
        self.delete_row_btn = QPushButton("删除行")
        self.new_col_btn = QPushButton("新增列")
        self.delete_col_btn = QPushButton("删除列")
        
        # 设置按钮样式
        for btn in [self.new_row_btn, self.delete_row_btn, self.new_col_btn, self.delete_col_btn]:
            btn.setMinimumHeight(30)  # 设置按钮最小高度
            btn.setEnabled(False)  # 初始时禁用所有按钮
            button_layout.addWidget(btn)
            
        button_group.setLayout(button_layout)
        toolbar_layout.addWidget(button_group)
        toolbar_layout.addStretch()
        bottom_layout.addLayout(toolbar_layout)
        
        # 右侧：Excel样式的表格
        table_group = QGroupBox("字段配置")
        table_layout = QVBoxLayout()
        
        self.kv_table = QTableWidget()
        self.kv_table.setColumnCount(2)
        self.kv_table.setHorizontalHeaderLabels(["字段名", "选项值"])
        
        # 设置表格样式
        self.kv_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                alternate-background-color: #f6f6f6;
            }
            QHeaderView::section {
                background-color: #e0e0e0;
                padding: 4px;
                border: 1px solid #d0d0d0;
                font-weight: bold;
            }
            QTableWidget::item {
                padding: 4px;
            }
        """)
        
        # 设置表格属性
        self.kv_table.setShowGrid(True)  # 显示网格线
        self.kv_table.setAlternatingRowColors(True)  # 交替行颜色
        self.kv_table.setSelectionBehavior(QTableWidget.SelectItems)  # 允许选择单元格
        self.kv_table.setSelectionMode(QTableWidget.ExtendedSelection)  # 允许多选
        self.kv_table.horizontalHeader().setStretchLastSection(True)
        self.kv_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)  # 第一列自适应内容
        self.kv_table.verticalHeader().setVisible(False)  # 隐藏行号
        self.kv_table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed)

        # 添加表格选择变化的信号连接
        self.kv_table.itemSelectionChanged.connect(self.on_table_selection_changed)
        self.kv_table.horizontalHeader().sectionClicked.connect(self.on_header_clicked)
        # 添加单元格编辑完成的信号连接
        self.kv_table.cellChanged.connect(self.on_cell_changed)
        
        table_layout.addWidget(self.kv_table)
        table_group.setLayout(table_layout)
        bottom_layout.addWidget(table_group, stretch=1)  # 表格占据更多空间
        
        layout.addLayout(bottom_layout)

        # 底部：关联设置和确认加载按钮
        bottom_buttons_layout = QHBoxLayout()
        bottom_buttons_layout.addStretch()
        
        self.relation_btn = QPushButton("关联设置")
        self.confirm_btn = QPushButton("确认加载")
        
        # 设置底部按钮样式
        for btn in [self.relation_btn, self.confirm_btn]:
            btn.setMinimumHeight(40)  # 设置按钮最小高度
            btn.setMinimumWidth(120)  # 设置按钮最小宽度
            btn.setEnabled(False)  # 初始时禁用所有按钮
            bottom_buttons_layout.addWidget(btn)
        
        layout.addLayout(bottom_buttons_layout)
        
        self.setLayout(layout)
        
        # 连接信号
        self.new_row_btn.clicked.connect(self.on_new_row)
        self.delete_row_btn.clicked.connect(self.on_delete_row)
        self.new_col_btn.clicked.connect(self.on_new_column)
        self.delete_col_btn.clicked.connect(self.on_delete_column)
        self.relation_btn.clicked.connect(self.on_relation_setting)
        self.confirm_btn.clicked.connect(self.on_confirm_load)

        # Initial state update
        self.update_toolbar_buttons()

    def update_toolbar_buttons(self):
        """更新工具栏按钮状态"""
        has_data = self.current_json_type in self.json_data and self.json_data[self.current_json_type] is not None
        is_list = isinstance(self.json_data.get(self.current_json_type, None), list)
        
        # 检查是否有选中的单元格
        selected_items = self.kv_table.selectedItems()
        has_selection = len(selected_items) > 0
        
        # 检查是否有选中的列
        selected_columns = set()
        for item in selected_items:
            selected_columns.add(item.column())
        has_selected_column = len(selected_columns) > 0

        # 根据当前字段类型决定是否启用编辑按钮
        if self.current_json_type == "单独列字段":
            # 单独列字段只启用行操作按钮
            self.new_row_btn.setEnabled(has_data and is_list)
            self.delete_row_btn.setEnabled(has_data and is_list and has_selection)
            self.new_col_btn.setEnabled(False)
            self.delete_col_btn.setEnabled(False)
        elif self.current_json_type == "固定字段":
            # 固定字段启用所有按钮
            self.new_row_btn.setEnabled(has_data and is_list)
            self.delete_row_btn.setEnabled(has_data and is_list and has_selection)
            self.new_col_btn.setEnabled(has_data)
            self.delete_col_btn.setEnabled(has_data and has_selected_column)
        else:  # 可变字段
            # 可变字段启用所有按钮
            self.new_row_btn.setEnabled(has_data and is_list)
            self.delete_row_btn.setEnabled(has_data and is_list and has_selection)
            self.new_col_btn.setEnabled(has_data)
            self.delete_col_btn.setEnabled(has_data and has_selected_column)

        # 底部按钮状态更新
        has_any_json_data = any(data is not None for data in self.json_data.values())
        self.relation_btn.setEnabled(has_any_json_data)
        self.confirm_btn.setEnabled(has_any_json_data)

    def on_relation_setting(self):
        """打开关联设置对话框，处理所有已加载的JSON数据"""
        if not any(data is not None for data in self.json_data.values()):
            QMessageBox.warning(self, "警告", "没有可用的JSON数据")
            return
            
        dialog = RelationSettingDialog(self.json_data, self)
        dialog.exec_()

    def on_confirm_load(self):
        """确认加载所有已加载的JSON文件配置"""
        try:
            # 统一使用config目录
            field_definitions_path = os.path.join(os.getcwd(), 'config', 'main_table_field_definitions.json')
            field_groups_path = os.path.join(os.getcwd(), 'config', 'field_groups.json')
            # 合并所有字段定义，支持list类型，自动推断类型和选项
            all_fields = {}
            # 修改这里：使用字典来存储每个字段的有序值列表
            value_collections = {}  # key: list (有序，去重后的值列表)

            for field_type in ["单独列字段", "固定字段", "可变字段"]:
                data = self.json_data.get(field_type)
                if isinstance(data, list):
                    for row in data:
                        if isinstance(row, dict):
                            for key, value in row.items():
                                if key not in ("type", "单独列字段", "固定字段", "可变字段"):
                                    if key not in value_collections:
                                        value_collections[key] = [] # Initialize with a list

                                    # Collect all non-empty values, preserving order and avoiding duplicates
                                    values_to_add = []
                                    if isinstance(value, list):
                                         values_to_add = [v for v in value if v not in (None, "")]
                                    elif value not in (None, ""):
                                         values_to_add = [value]

                                    for val in values_to_add:
                                         # Check if value is already in the collected list for this key
                                         if val not in value_collections[key]:
                                              value_collections[key].append(val) # Append to list

            # 过滤
            all_fields = filter_valid_fields(all_fields)

            # 遍历收集到的值来确定字段类型和选项
            for key, values in value_collections.items(): # <-- Apply changes starting here
                # 类型优先级判断
                if "时间" in key:
                    type_ = "QDateTimeEdit"
                    options = []
                elif "日期" in key:
                    type_ = "QDateEdit"
                    options = []
                elif any(word in key for word in ["描述", "情况", "详情", "原因", "措施", "简述"]):
                    type_ = "QTextEdit"
                    options = []
                elif len(values) > 0:
                    type_ = "QComboBox"
                    # Options are already in the desired order in the list
                    options = [str(v) for v in values] # No need for sorted() or set conversion
                else:
                    type_ = "QLineEdit"
                    options = []
                all_fields[key] = {"type": type_, "options": options}

            # 过滤，确保只包含有效的字段定义
            all_fields = filter_valid_fields(all_fields)

            if not all_fields:
                QMessageBox.warning(self, "无可用字段", "未检测到任何有效字段，未保存。请先加载有效的字段定义。")
                return

            # 保存到文件时只保存 all_fields
            try:
                with open(field_definitions_path, 'w', encoding='utf-8') as f:
                    json.dump(all_fields, f, ensure_ascii=False, indent=4)
            except Exception as e:
                QMessageBox.critical(self, "保存错误", f"保存字段定义时发生错误：{str(e)}")
                return
            # 生成field_groups，严格按导入的三个JSON文件内容分类
            field_groups = {"单独列字段": [], "固定字段": [], "可变字段": []}
            for group_type in ["单独列字段", "固定字段", "可变字段"]:
                data = self.json_data.get(group_type)
                if isinstance(data, list):
                    for row in data:
                        if isinstance(row, dict):
                            for k in row.keys():
                                if k not in ("type", "单独列字段", "固定字段", "可变字段") and k not in field_groups[group_type]:
                                    field_groups[group_type].append(k)
                elif isinstance(data, dict):
                    for k in data.keys():
                        if k not in ("type", "单独列字段", "固定字段", "可变字段") and k not in field_groups[group_type]:
                            field_groups[group_type].append(k)
            # 保存field_groups
            try:
                with open(field_groups_path, 'w', encoding='utf-8') as f:
                    json.dump(field_groups, f, ensure_ascii=False, indent=4)
            except Exception as e:
                QMessageBox.critical(self, "保存错误", f"保存字段分组时发生错误：{str(e)}")
                return
            # 更新主表的字段定义
            if hasattr(self.parent(), 'update_field_definitions'):
                self.parent().update_field_definitions(all_fields)
                self.parent()._save_field_definitions()  # 确保保存更新后的字段定义
            # 发射信号
            self.fields_imported.emit(all_fields)
            self.accept()
        except Exception as e:
            QMessageBox.critical(self, "错误", f"处理JSON数据时发生错误：{str(e)}")
            logging.error(f"Error in on_confirm_load: {str(e)}")

    def on_browse_json_file(self, json_type):
        """浏览并选择JSON文件"""
        last_dir = ""
        if self.json_file_paths[json_type] and isinstance(self.json_file_paths[json_type], QLineEdit):
            last_path = self.json_file_paths[json_type].text()
            if last_path and os.path.exists(last_path):
                last_dir = os.path.dirname(last_path)
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            f"选择{json_type}JSON文件",
            last_dir,
            "JSON Files (*.json)"
        )
        if file_path:
            self.json_file_paths[json_type].setText(file_path)
            self.load_json_file(json_type, file_path)
            self._save_paths()

    def load_json_file(self, json_type, file_path):
        """加载JSON文件内容，异常处理，UI同步"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            # 新增：过滤
            if json_type == "主表字段定义" or os.path.basename(file_path) == "main_table_field_definitions.json":
                data = filter_valid_fields(data)
            self.json_data[json_type] = data
            self.update_json_file_list()
            self.display_json_data(json_type)
        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载JSON文件失败：{str(e)}")
            self.json_data[json_type] = None
            self.update_json_file_list()
            self.kv_table.clear()

    def display_json_data(self, json_type):
        """将JSON数据转换为表格，支持字典/数组，列顺序与JSON一致"""
        if json_type not in self.json_data or self.json_data[json_type] is None:
            self.kv_table.clear()
            self.current_json_type = None
            self.update_toolbar_buttons()
            return

        self.current_json_type = json_type
        data = self.json_data[json_type]
        parsed_rows = []
        all_keys = []
        def process_value(value):
            if isinstance(value, (dict, list)):
                return value
            if isinstance(value, str):
                try:
                    value = value.replace("'", '"')
                    if value.endswith(','):
                        value = value[:-1]
                    parsed = json.loads(value)
                    return parsed
                except:
                    if ':' in value:
                        try:
                            key, val = value.split(':', 1)
                            return {key.strip().strip('"\''): val.strip().strip('"\'')}
                        except:
                            pass
            return value
        def extract_key_value_pairs(obj):
            if isinstance(obj, dict):
                return obj
            elif isinstance(obj, list):
                result = {}
                for item in obj:
                    if isinstance(item, dict):
                        result.update(item)
                    elif isinstance(item, str):
                        processed = process_value(item)
                        if isinstance(processed, dict):
                            result.update(processed)
                return result
            elif isinstance(obj, str):
                processed = process_value(obj)
                if isinstance(processed, dict):
                    return processed
            return {}
        if isinstance(data, list):
            for row in data:
                row_dict = {}
                if isinstance(row, (dict, str, list)):
                    processed = extract_key_value_pairs(row)
                    if isinstance(processed, dict):
                        row_dict.update(processed)
                        for k in processed.keys():
                            if k not in all_keys and k and k.strip():
                                all_keys.append(k)
                parsed_rows.append(row_dict)
        elif isinstance(data, dict):
            parsed_rows = [data]
            for k in data.keys():
                if k not in all_keys and k and k.strip():
                    all_keys.append(k)
        self.kv_table.setColumnCount(len(all_keys))
        self.kv_table.setHorizontalHeaderLabels(all_keys)
        self.kv_table.setRowCount(len(parsed_rows))
        for row_idx, row_dict in enumerate(parsed_rows):
            for col_idx, key in enumerate(all_keys):
                value = row_dict.get(key, "")
                if isinstance(value, list):
                    value = ", ".join(str(v) for v in value)
                else:
                    value = str(value)
                item = QTableWidgetItem(value)
                item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
                if isinstance(row_dict.get(key), list):
                    item.setBackground(QColor("#e6f3ff"))
                elif isinstance(row_dict.get(key), (int, float)):
                    item.setBackground(QColor("#e6ffe6"))
                elif isinstance(row_dict.get(key), bool):
                    item.setBackground(QColor("#fff2e6"))
                elif not value.strip():
                    item.setBackground(QColor("#f2f2f2"))
                self.kv_table.setItem(row_idx, col_idx, item)
        self.kv_table.resizeColumnsToContents()
        for row in range(self.kv_table.rowCount()):
            self.kv_table.resizeRowToContents(row)
        self.update_toolbar_buttons()
        self.update_table_editable()

    def on_cell_changed(self, row, column):
        """当单元格内容改变时，同步更新JSON数据并触发延迟保存"""
        if not self.current_json_type or self.current_json_type not in self.json_data:
            return

        try:
            # 获取列名（键）
            key = self.kv_table.horizontalHeaderItem(column).text()
            if not key:
                return

            # 获取新的单元格值
            item = self.kv_table.item(row, column)
            if not item:
                return
            new_value = item.text()

            # 根据JSON数据类型处理更新
            data = self.json_data[self.current_json_type]
            if isinstance(data, list):
                if 0 <= row < len(data):
                    # 尝试将值转换为列表（如果包含逗号）
                    if ',' in new_value:
                        try:
                            # 尝试解析为列表
                            value = [v.strip() for v in new_value.split(',')]
                        except:
                            value = new_value
                    else:
                        value = new_value
                    data[row][key] = value
            elif isinstance(data, dict):
                # 尝试将值转换为列表（如果包含逗号）
                if ',' in new_value:
                    try:
                        # 尝试解析为列表
                        value = [v.strip() for v in new_value.split(',')]
                    except:
                        value = new_value
                else:
                    value = new_value
                data[key] = value

            # 触发延迟保存
            self._trigger_delayed_save(self.current_json_type)

        except Exception as e:
            logging.error(f"Error updating cell value: {str(e)}")
            QMessageBox.warning(self, "更新错误", f"更新单元格值时发生错误：{str(e)}")

    def _trigger_delayed_save(self, json_type):
        """触发延迟保存"""
        self.pending_save_type = json_type
        self.save_timer.start(1000)  # 1秒后执行保存

    def _delayed_save(self):
        """执行延迟保存"""
        if self.pending_save_type:
            self.save_json_file(self.pending_save_type)
            self.pending_save_type = None

    def save_json_file(self, json_type):
        """保存JSON数据到文件"""
        if json_type not in self.json_file_paths or not self.json_file_paths[json_type].text():
            return

        file_path = self.json_file_paths[json_type].text()
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            
            # 保存数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.json_data[json_type], f, ensure_ascii=False, indent=4)
            
            logging.info(f"Successfully saved JSON data to {file_path}")
        except Exception as e:
            logging.error(f"Error saving JSON file: {str(e)}")
            QMessageBox.critical(self, "保存错误", f"保存JSON文件时发生错误：{str(e)}")

    def on_new_row(self):
        """添加新行（仅支持list类型JSON）"""
        if not self.current_json_type or self.current_json_type not in self.json_data:
            return
        data = self.json_data[self.current_json_type]
        if isinstance(data, list):
            # 新增空行
            new_row = {}
            self.json_data[self.current_json_type].append(new_row)
            self.display_json_data(self.current_json_type)
            self._trigger_delayed_save(self.current_json_type)

    def on_delete_row(self):
        """删除选中的行（仅支持list类型JSON）"""
        if not self.current_json_type or self.current_json_type not in self.json_data:
            return
        data = self.json_data[self.current_json_type]
        if isinstance(data, list):
            current_row = self.kv_table.currentRow()
            if 0 <= current_row < len(data):
                del self.json_data[self.current_json_type][current_row]
                self.display_json_data(self.current_json_type)
                self._trigger_delayed_save(self.current_json_type)

    def on_new_column(self):
        """添加新列（所有行都加新key，值为空）"""
        if not self.current_json_type or self.current_json_type not in self.json_data:
            return
        key, ok = QInputDialog.getText(self, "新增列", "请输入新字段名:")
        if ok and key:
            data = self.json_data[self.current_json_type]
            if isinstance(data, list):
                for row in data:
                    if isinstance(row, dict):
                        row[key] = ""
            elif isinstance(data, dict):
                data[key] = ""
            self.display_json_data(self.current_json_type)
            self._trigger_delayed_save(self.current_json_type)

    def on_delete_column(self):
        """删除当前选中列"""
        if not self.current_json_type or self.current_json_type not in self.json_data:
            return
        col = self.kv_table.currentColumn()
        if col < 0:
            return
        key = self.kv_table.horizontalHeaderItem(col).text()
        data = self.json_data[self.current_json_type]
        if isinstance(data, list):
            for row in data:
                if isinstance(row, dict) and key in row:
                    del row[key]
        elif isinstance(data, dict):
            if key in data:
                del data[key]
        self.display_json_data(self.current_json_type)
        self._trigger_delayed_save(self.current_json_type)

    def _load_saved_paths(self):
        """加载保存的JSON文件路径"""
        try:
            if os.path.exists(self.config_file_path):
                with open(self.config_file_path, 'r', encoding='utf-8') as f:
                    saved_paths = json.load(f)
                    for json_type, path in saved_paths.items():
                        if json_type in self.json_file_paths and os.path.exists(path):
                            self.json_file_paths[json_type].setText(path)
                            self.load_json_file(json_type, path)
        except Exception as e:
            logging.warning(f"加载保存的JSON文件路径失败: {str(e)}")

    def _save_paths(self):
        """保存JSON文件路径到配置文件"""
        try:
            os.makedirs(os.path.dirname(self.config_file_path), exist_ok=True)
            paths_to_save = {k: v.text() for k, v in self.json_file_paths.items() if v.text()}
            with open(self.config_file_path, 'w', encoding='utf-8') as f:
                json.dump(paths_to_save, f, ensure_ascii=False, indent=4)
        except Exception as e:
            logging.warning(f"保存JSON文件路径失败: {str(e)}")

    def on_json_file_list_item_clicked(self, item):
        """当点击文件列表项时显示对应的JSON数据"""
        json_type = item.text()
        self.current_json_type = json_type
        self.display_json_data(json_type)
        self.update_toolbar_buttons()  # 更新按钮状态
        self.update_table_editable()  # 更新表格可编辑状态
        
    def update_json_file_list(self):
        """更新JSON文件列表"""
        self.json_file_list.clear()
        for json_type, data in self.json_data.items():
            if data is not None:
                self.json_file_list.addItem(json_type)

    def update_table_editable(self):
        """根据当前字段类型设置表格是否可编辑"""
        # 所有字段类型都允许编辑
        self.kv_table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed)

    def on_table_selection_changed(self):
        """当表格选择变化时更新按钮状态"""
        self.update_toolbar_buttons()

    def on_header_clicked(self, column):
        """当点击表头时更新按钮状态"""
        # 清除其他选择，只选中当前列
        self.kv_table.clearSelection()
        for row in range(self.kv_table.rowCount()):
            item = self.kv_table.item(row, column)
            if item:
                item.setSelected(True)
        self.update_toolbar_buttons()

    def get_permission(self):
        if os.path.exists(FIELD_PERMISSION_PATH):
            try:
                with open(FIELD_PERMISSION_PATH, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}

    def update_table_editable(self):
        """根据当前字段类型设置表格是否可编辑"""
        # 所有字段类型都允许编辑
        self.kv_table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed)

    def on_table_selection_changed(self):
        """当表格选择变化时更新按钮状态"""
        self.update_toolbar_buttons()

    def on_header_clicked(self, column):
        """当点击表头时更新按钮状态"""
        # 清除其他选择，只选中当前列
        self.kv_table.clearSelection()
        for row in range(self.kv_table.rowCount()):
            item = self.kv_table.item(row, column)
            if item:
                item.setSelected(True)
        self.update_toolbar_buttons()

class RelationSettingDialog(QDialog):
    """关联设置对话框"""
    def __init__(self, json_data, parent=None):
        super().__init__(parent)
        self.setWindowTitle("关联设置")
        self.json_data = json_data
        self.relation_groups = []  # 存储关联组信息
        
        self._init_ui()
        self.load_relation_groups()
        
    def _init_ui(self):
        layout = QVBoxLayout()
        
        # 关联组列表
        self.relation_list = QListWidget()
        layout.addWidget(self.relation_list)
        
        # 按钮区域
        btn_layout = QHBoxLayout()
        self.new_group_btn = QPushButton("新增关联组")
        self.edit_group_btn = QPushButton("修改关联组")
        self.delete_group_btn = QPushButton("删除关联组")
        
        for btn in [self.new_group_btn, self.edit_group_btn, self.delete_group_btn]:
            btn_layout.addWidget(btn)
            
        layout.addLayout(btn_layout)
        
        # 连接信号
        self.new_group_btn.clicked.connect(self.on_new_group)
        self.edit_group_btn.clicked.connect(self.on_edit_group)
        self.delete_group_btn.clicked.connect(self.on_delete_group)
        
        self.setLayout(layout)
        
    def load_relation_groups(self):
        """加载关联组（示例：从配置文件加载）"""
        # TODO: 从配置文件加载关联组
        self.update_relation_list()
        
    def update_relation_list(self):
        """更新关联组列表显示"""
        self.relation_list.clear()
        for group in self.relation_groups:
            self.relation_list.addItem(f"{group['col1']} <-> {group['col2']}")
            
    def on_new_group(self):
        """新增关联组"""
        dialog = RelationGroupDialog(self.json_data, self)
        if dialog.exec_() == QDialog.Accepted:
            col1, col2 = dialog.get_selected_columns()
            if col1 and col2:
                self.relation_groups.append({"col1": col1, "col2": col2})
                self.update_relation_list()
                self.save_relation_groups()
                
    def on_edit_group(self):
        """修改关联组"""
        current_item = self.relation_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择一个关联组")
            return
            
        index = self.relation_list.row(current_item)
        group = self.relation_groups[index]
        
        dialog = RelationGroupDialog(self.json_data, self, group["col1"], group["col2"])
        if dialog.exec_() == QDialog.Accepted:
            col1, col2 = dialog.get_selected_columns()
            if col1 and col2:
                self.relation_groups[index] = {"col1": col1, "col2": col2}
                self.update_relation_list()
                self.save_relation_groups()
                
    def on_delete_group(self):
        """删除关联组"""
        current_item = self.relation_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择一个关联组")
            return
            
        index = self.relation_list.row(current_item)
        self.relation_groups.pop(index)
        self.update_relation_list()
        self.save_relation_groups()
        
    def save_relation_groups(self):
        """保存关联组（示例：保存到配置文件）"""
        # TODO: 将关联组保存到配置文件
        pass

class RelationGroupDialog(QDialog):
    """关联组选择对话框"""
    def __init__(self, json_data, parent=None, pre_selected_col1=None, pre_selected_col2=None):
        super().__init__(parent)
        self.setWindowTitle("选择关联列")
        self.json_data = json_data
        self.pre_selected_col1 = pre_selected_col1
        self.pre_selected_col2 = pre_selected_col2
        self.selected_col1 = None
        self.selected_col2 = None
        
        self._init_ui()
        
    def _init_ui(self):
        layout = QVBoxLayout()
        
        # 获取所有可选的列名
        all_columns = set()
        for data in self.json_data.values():
            if isinstance(data, dict):
                all_columns.update(data.keys())
                
        # 创建两个列表用于选择列
        h_layout = QHBoxLayout()
        
        # 第一列选择
        col1_group = QGroupBox("第一列")
        col1_layout = QVBoxLayout()
        self.col1_list = QListWidget()
        for col in sorted(all_columns):
            item = QListWidgetItem(col)
            self.col1_list.addItem(item)
            if col == self.pre_selected_col1:
                item.setSelected(True)
        col1_layout.addWidget(self.col1_list)
        col1_group.setLayout(col1_layout)
        h_layout.addWidget(col1_group)
        
        # 第二列选择
        col2_group = QGroupBox("第二列")
        col2_layout = QVBoxLayout()
        self.col2_list = QListWidget()
        for col in sorted(all_columns):
            item = QListWidgetItem(col)
            self.col2_list.addItem(item)
            if col == self.pre_selected_col2:
                item.setSelected(True)
        col2_layout.addWidget(self.col2_list)
        col2_group.setLayout(col2_layout)
        h_layout.addWidget(col2_group)
        
        layout.addLayout(h_layout)
        
        # 确定和取消按钮
        btn_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self)
        btn_box.accepted.connect(self.accept)
        btn_box.rejected.connect(self.reject)
        layout.addWidget(btn_box)
        
        self.setLayout(layout)
        
    def get_selected_columns(self):
        """获取选中的列名"""
        col1_item = self.col1_list.currentItem()
        col2_item = self.col2_list.currentItem()
        
        if col1_item and col2_item:
            self.selected_col1 = col1_item.text()
            self.selected_col2 = col2_item.text()
            return self.selected_col1, self.selected_col2
        return None, None

class UploadInfoDialog(FieldImportDialog):
    """上传信息设置窗口（具有完整功能和权限控制）"""
    def __init__(self, parent=None):
        # 获取父窗口的管理员模式状态
        self.is_admin_mode = getattr(parent, 'is_admin_mode', False) if parent else False

        super().__init__(parent)
        self.setWindowTitle("上传信息设置")

        # 更新配置文件路径为上传信息专用
        self.config_file_path = os.path.join(os.getcwd(), 'config', 'upload_info_paths.json')

        # 重新加载保存的路径
        self._load_saved_paths()

        # 根据权限设置初始显示
        self._setup_permission_based_ui()

    def _setup_permission_based_ui(self):
        """根据权限设置UI"""
        if not self.is_admin_mode:
            # 非管理员模式：检查权限配置
            permission = self.get_permission()

            # 如果没有权限配置文件或权限为False，则隐藏对应的JSON类型
            allowed_types = []
            for json_type in ["单独列字段", "固定字段", "可变字段"]:
                if permission.get(json_type, False):  # 默认为False，需要明确设置为True才允许
                    allowed_types.append(json_type)

            # 更新JSON文件列表，只显示允许的类型
            if hasattr(self, 'json_file_list'):
                self.json_file_list.clear()
                for json_type in allowed_types:
                    if json_type in self.json_data and self.json_data[json_type] is not None:
                        self.json_file_list.addItem(json_type)

            # 如果有允许的类型，默认显示第一个
            if allowed_types and allowed_types[0] in self.json_data:
                self.display_json_data(allowed_types[0])
            elif "可变字段" in self.json_data:
                # 如果没有特别允许的类型，默认显示可变字段（向后兼容）
                self.display_json_data("可变字段")
        else:
            # 管理员模式：显示所有功能，默认显示可变字段
            self.display_json_data("可变字段")

    def update_toolbar_buttons(self):
        """更新工具栏按钮状态（带权限控制）"""
        has_data = self.current_json_type in self.json_data and self.json_data[self.current_json_type] is not None
        is_list = isinstance(self.json_data.get(self.current_json_type, None), list)

        # 检查是否有选中的单元格
        selected_items = self.kv_table.selectedItems()
        has_selection = len(selected_items) > 0

        # 检查是否有选中的列
        selected_columns = set()
        for item in selected_items:
            selected_columns.add(item.column())
        has_selected_column = len(selected_columns) > 0

        # 权限检查
        if not self.is_admin_mode:
            permission = self.get_permission()
            current_type_allowed = permission.get(self.current_json_type, False)

            if not current_type_allowed:
                # 如果当前字段类型不允许编辑，禁用所有编辑按钮
                self.new_row_btn.setEnabled(False)
                self.delete_row_btn.setEnabled(False)
                self.new_col_btn.setEnabled(False)
                self.delete_col_btn.setEnabled(False)

                # 底部按钮状态更新
                has_any_json_data = any(data is not None for data in self.json_data.values())
                self.relation_btn.setEnabled(has_any_json_data)
                self.confirm_btn.setEnabled(has_any_json_data)
                return

        # 根据当前字段类型决定是否启用编辑按钮
        if self.current_json_type in ["单独列字段", "固定字段"]:
            # 单独列字段和固定字段只启用行操作按钮
            self.new_row_btn.setEnabled(has_data and is_list)
            self.delete_row_btn.setEnabled(has_data and is_list and has_selection)
            self.new_col_btn.setEnabled(False)
            self.delete_col_btn.setEnabled(False)
        else:  # 可变字段
            # 可变字段启用所有按钮
            self.new_row_btn.setEnabled(has_data and is_list)
            self.delete_row_btn.setEnabled(has_data and is_list and has_selection)
            self.new_col_btn.setEnabled(has_data)
            self.delete_col_btn.setEnabled(has_data and has_selected_column)

        # 底部按钮状态更新
        has_any_json_data = any(data is not None for data in self.json_data.values())
        self.relation_btn.setEnabled(has_any_json_data)
        self.confirm_btn.setEnabled(has_any_json_data)

    def update_table_editable(self):
        """根据当前字段类型和权限设置表格是否可编辑"""
        if not self.is_admin_mode:
            permission = self.get_permission()
            current_type_allowed = permission.get(self.current_json_type, False)

            if not current_type_allowed:
                # 如果当前字段类型不允许编辑，设置为只读
                self.kv_table.setEditTriggers(QTableWidget.NoEditTriggers)
                return

        # 允许编辑
        self.kv_table.setEditTriggers(QTableWidget.DoubleClicked | QTableWidget.EditKeyPressed)

    def on_relation_setting(self):
        """关联设置（重写以添加权限设置功能）"""
        if self.is_admin_mode:
            # 管理员模式：显示权限设置选项
            from PyQt5.QtWidgets import QMenu
            menu = QMenu(self)

            # 添加原有的关联设置功能
            relation_action = menu.addAction("字段关联设置")
            relation_action.triggered.connect(self._show_relation_dialog)

            # 添加权限设置功能
            permission_action = menu.addAction("权限设置")
            permission_action.triggered.connect(self._show_permission_dialog)

            # 在按钮位置显示菜单
            menu.exec_(self.relation_btn.mapToGlobal(self.relation_btn.rect().bottomLeft()))
        else:
            # 非管理员模式：只显示关联设置
            self._show_relation_dialog()

    def _show_relation_dialog(self):
        """显示关联设置对话框"""
        # 调用父类的关联设置方法
        super().on_relation_setting()

    def _show_permission_dialog(self):
        """显示权限设置对话框"""
        try:
            dialog = PermissionSettingDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                # 权限设置已更新，重新设置UI
                self._setup_permission_based_ui()
                self.update_toolbar_buttons()
                QMessageBox.information(self, "成功", "权限设置已更新")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"权限设置失败: {e}")

    def update_json_file_list(self):
        """更新JSON文件列表（重写以支持权限控制）"""
        self.json_file_list.clear()

        if not self.is_admin_mode:
            # 非管理员模式：只显示有权限的字段类型
            permission = self.get_permission()
            for json_type, data in self.json_data.items():
                if data is not None and permission.get(json_type, False):
                    self.json_file_list.addItem(json_type)
        else:
            # 管理员模式：显示所有已加载的字段类型
            for json_type, data in self.json_data.items():
                if data is not None:
                    self.json_file_list.addItem(json_type)

class PermissionSettingDialog(QDialog):
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("字段修改权限设置")
        self.setMinimumWidth(400)
        self.setMinimumHeight(200)
        self.permission = self.load_permission()
        layout = QVBoxLayout()
        self.checks = {}
        for field in ["单独列字段", "固定字段", "可变字段"]:
            h = QHBoxLayout()
            label = QLabel(field)
            cb = QCheckBox("对所有用户开放设置")
            cb.setChecked(self.permission.get(field, False))
            h.addWidget(label)
            h.addStretch()
            h.addWidget(cb)
            layout.addLayout(h)
            self.checks[field] = cb
        btns = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        btns.accepted.connect(self.accept)
        btns.rejected.connect(self.reject)
        layout.addWidget(btns)
        self.setLayout(layout)
    def load_permission(self):
        if os.path.exists(FIELD_PERMISSION_PATH):
            try:
                with open(FIELD_PERMISSION_PATH, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception:
                return {}
        return {}
    def save_permission(self):
        data = {k: v.isChecked() for k, v in self.checks.items()}
        os.makedirs(os.path.dirname(FIELD_PERMISSION_PATH), exist_ok=True)
        with open(FIELD_PERMISSION_PATH, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=4)
    def exec_(self):
        result = super().exec_()
        if result == QDialog.Accepted:
            self.save_permission()
        return result
class FilterSelectorDialog(QDialog):
    def __init__(self, header_items, current_visible_original_indices, parent=None, visual_order=None):
        super().__init__(parent)
        self.setWindowTitle("问题表显示设置")
        self.setMinimumWidth(800)
        self.setMinimumHeight(600)
        
        # 添加配置文件路径
        self.config_file_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            "config",
            "column_order.json"
        )
        
        self.header_items = header_items
        self.current_visible_original_indices = current_visible_original_indices
        self.visual_order = visual_order  # 当前表格的视觉顺序
        self._init_ui()
        
        # 如果提供了视觉顺序，则使用它；否则加载保存的列顺序
        if self.visual_order:
            self._apply_visual_order()
        else:
            self._load_column_order()
        
    def _init_ui(self):
        main_layout = QVBoxLayout(self)
        self.list_widget = QListWidget()
        self.list_widget.setDragDropMode(QAbstractItemView.InternalMove)
        self.list_widget.setDefaultDropAction(Qt.MoveAction)
        self.list_widget.setSelectionMode(QAbstractItemView.SingleSelection)
        self.list_widget.setDragEnabled(True)
        self.list_widget.setAcceptDrops(True)
        self.list_widget.setDropIndicatorShown(True)
        self.list_widget.setStyleSheet('''
            QListWidget {
                border: 1px solid #ddd;
                background-color: white;
                border-radius: 4px;
                padding: 5px;
            }
            QListWidget::item {
                padding: 5px;
            }
            QListWidget::item:selected {
                background-color: #e0e0e0; /* Light grey for selected item */
            }
        ''')

        # 初始填充列表（此部分将在 _load_column_order 中根据保存的顺序重写）
        for i, header_text in enumerate(self.header_items):
            item = QListWidgetItem(header_text)
            item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsUserCheckable | Qt.ItemIsEnabled | Qt.ItemIsDragEnabled)
            item.setData(Qt.UserRole, i) # Store original index
            
            if i == 0: # Assuming "全不选" is always at index 0
                item.setFlags(item.flags() & ~Qt.ItemIsDragEnabled) # Make "全不选" not draggable
                item.setCheckState(Qt.Checked)
            elif i in self.current_visible_original_indices:
                item.setCheckState(Qt.Checked)
            else:
                item.setCheckState(Qt.Unchecked)
            self.list_widget.addItem(item)

        main_layout.addWidget(self.list_widget)

        # 按钮布局
        button_layout = QHBoxLayout()
        self.select_all_button = QPushButton("全选")
        self.select_all_button.clicked.connect(self.select_all)
        self.clear_all_button = QPushButton("清除")
        self.clear_all_button.clicked.connect(self.clear_all)

        button_layout.addWidget(self.select_all_button)
        button_layout.addWidget(self.clear_all_button)
        button_layout.addStretch()

        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept)
        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)

        button_layout.addWidget(self.ok_button)
        button_layout.addWidget(self.cancel_button)
        main_layout.addLayout(button_layout)
    
    def _load_column_order(self):
        """加载保存的列顺序"""
        try:
            if os.path.exists(self.config_file_path):
                with open(self.config_file_path, 'r', encoding='utf-8') as f:
                    saved_order = json.load(f)

                    if isinstance(saved_order, list) and saved_order:
                        # Store original items info (text, check state, original index) for quick lookup
                        original_items_info = {}
                        for i, header_text in enumerate(self.header_items):
                            is_checked = i in self.current_visible_original_indices
                            original_items_info[header_text] = {
                                'check_state': Qt.Checked if is_checked else Qt.Unchecked,
                                'original_index': i
                            }

                        # Clear the list widget
                        self.list_widget.clear()

                        # Re-add items in the saved order
                        added_headers_from_saved_order = set()
                        for header_name in saved_order:
                            if header_name in original_items_info:
                                info = original_items_info[header_name]
                                new_item = QListWidgetItem(header_name)
                                new_item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsUserCheckable | Qt.ItemIsEnabled | Qt.ItemIsDragEnabled)
                                new_item.setCheckState(info['check_state'])
                                new_item.setData(Qt.UserRole, info['original_index'])
                                
                                # Ensure "全不选" is not draggable
                                if info['original_index'] == 0: # Assuming original index 0 is "全不选"
                                    new_item.setFlags(new_item.flags() & ~Qt.ItemIsDragEnabled)
                                
                                self.list_widget.addItem(new_item)
                                added_headers_from_saved_order.add(header_name)
                        
                        # Add any header_items that were not in the saved order (e.g., new columns)
                        # These will be appended at the end, maintaining their initial checked state.
                        for i, header_text in enumerate(self.header_items):
                            if header_text not in added_headers_from_saved_order:
                                # Create a new item if it was not re-added from saved order
                                item = QListWidgetItem(header_text)
                                item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsUserCheckable | Qt.ItemIsEnabled | Qt.ItemIsDragEnabled)
                                item.setData(Qt.UserRole, i) # Store original index
                                
                                # Set initial checked state based on current_visible_original_indices
                                if i == 0: # Assuming "全不选" is always at index 0 and always checked initially, but not draggable
                                    item.setFlags(item.flags() & ~Qt.ItemIsDragEnabled) # Make "全不选" not draggable
                                    item.setCheckState(Qt.Checked)
                                elif i in self.current_visible_original_indices:
                                    item.setCheckState(Qt.Checked)
                                else:
                                    item.setCheckState(Qt.Unchecked)
                                self.list_widget.addItem(item)

        except Exception as e:
            logging.warning(f"加载列顺序失败: {str(e)}")

    def _apply_visual_order(self):
        """应用当前表格的视觉顺序"""
        try:
            if not self.visual_order or not isinstance(self.visual_order, list):
                logging.warning("无效的视觉顺序")
                return
                
            # 创建映射：原始索引 -> 字段名称
            index_to_header = {}
            for i, header_text in enumerate(self.header_items):
                index_to_header[i] = header_text
                
            # 清空列表控件
            self.list_widget.clear()
            
            # 按照视觉顺序添加项目
            added_indices = set()
            for original_index in self.visual_order:
                if original_index in index_to_header:
                    header_text = index_to_header[original_index]
                    item = QListWidgetItem(header_text)
                    item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsUserCheckable | Qt.ItemIsEnabled | Qt.ItemIsDragEnabled)
                    item.setData(Qt.UserRole, original_index)
                    
                    # 设置选中状态
                    if original_index == 0:  # 假设 "全不选" 总是在索引 0
                        item.setFlags(item.flags() & ~Qt.ItemIsDragEnabled)  # 使 "全不选" 不可拖动
                        item.setCheckState(Qt.Checked)
                    elif original_index in self.current_visible_original_indices:
                        item.setCheckState(Qt.Checked)
                    else:
                        item.setCheckState(Qt.Unchecked)
                        
                    self.list_widget.addItem(item)
                    added_indices.add(original_index)
            
            # 添加未包含在视觉顺序中的项目
            for i, header_text in enumerate(self.header_items):
                if i not in added_indices:
                    item = QListWidgetItem(header_text)
                    item.setFlags(Qt.ItemIsSelectable | Qt.ItemIsUserCheckable | Qt.ItemIsEnabled | Qt.ItemIsDragEnabled)
                    item.setData(Qt.UserRole, i)
                    
                    # 设置选中状态
                    if i == 0:  # 假设 "全不选" 总是在索引 0
                        item.setFlags(item.flags() & ~Qt.ItemIsDragEnabled)  # 使 "全不选" 不可拖动
                        item.setCheckState(Qt.Checked)
                    elif i in self.current_visible_original_indices:
                        item.setCheckState(Qt.Checked)
                    else:
                        item.setCheckState(Qt.Unchecked)
                        
                    self.list_widget.addItem(item)
                    
            logging.info(f"成功应用视觉顺序: {self.visual_order}")
        except Exception as e:
            logging.warning(f"应用视觉顺序失败: {str(e)}")

    def _save_column_order(self):
        """保存列顺序到配置文件"""
        try:
            # 获取当前列表中的顺序（保存列名和原始索引）
            current_order = []
            current_order_indices = []
            for i in range(self.list_widget.count()):
                item = self.list_widget.item(i)
                if item:
                    current_order.append(item.text())
                    current_order_indices.append(item.data(Qt.UserRole))
            
            # 保存列名顺序到文件
            os.makedirs(os.path.dirname(self.config_file_path), exist_ok=True)
            with open(self.config_file_path, 'w', encoding='utf-8') as f:
                json.dump(current_order, f, ensure_ascii=False, indent=4)
                
            # 记录保存的列顺序
            logging.info(f"成功保存列顺序: {current_order}")
            logging.info(f"对应的原始索引: {current_order_indices}")
        except Exception as e:
            logging.warning(f"保存列顺序失败: {str(e)}")
            
    def select_all(self):
        """全选所有项目"""
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            item.setCheckState(Qt.Checked)

    def clear_all(self):
        """清除所有项目的选择"""
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            item.setCheckState(Qt.Unchecked)

    def get_final_order_and_checked_state(self):
        """返回最终的列顺序（原始索引）和所有已选中的列的原始索引"""
        final_order = []
        checked_indices = []
        for i in range(self.list_widget.count()):
            item = self.list_widget.item(i)
            if item:
                original_index = item.data(Qt.UserRole)
                final_order.append(original_index)
                if item.checkState() == Qt.Checked:
                    checked_indices.append(original_index)
        return final_order, checked_indices
    
    def get_selected_items(self):
        """返回所有选中项的原始索引"""
        _, checked_indices = self.get_final_order_and_checked_state()
        return checked_indices

    def accept(self):
        """对话框确认时保存列顺序"""
        self._save_column_order()
        super().accept()

# Example usage (for testing, not part of production code in this file)
if __name__ == '__main__':
    from PyQt5.QtWidgets import QApplication, QTextEdit
    import sys
    app = QApplication(sys.argv)
    # Mockup for all_field_names that ProblemEditorDock would provide
    mock_all_fields = [f"Field_{i}" for i in range(1, 21)] 
    mock_main_window = lambda: None # Mock main_window
    mock_main_window.problem_editor_dock = lambda: None
    mock_main_window.problem_editor_dock.field_definitions = {name: {"type": "QLineEdit"} for name in mock_all_fields}
    
    # Ensure a default config file exists for the test
    if not os.path.exists(os.path.join("config", "problem_form_groups.json")):
         save_group_config({"groups":[], "all_known_fields": list(mock_all_fields)})

    dialog = GroupSettingsDialog(main_window=mock_main_window, all_field_names=mock_all_fields)
    dialog.show()
    sys.exit(app.exec_())


class FieldMappingDialog(QDialog):
    """字段映射配置对话框"""

    def __init__(self, parent=None, main_window=None):
        super().__init__(parent)
        self.main_window = main_window
        self.setWindowTitle("字段映射配置")
        self.setMinimumSize(1200, 800)
        self.setModal(True)

        # 当前选择的数据表
        self.current_table = "试验问题表"  # 默认选择试验问题表，保持向后兼容

        # 获取目标字段列表（根据当前选择的数据表）
        self.target_fields = self._get_target_fields()

        # 获取默认配置文件路径
        self.default_config_path = self._get_default_config_path()

        self.init_ui()
        self.load_default_mapping()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 数据表选择区域
        table_group = QGroupBox("数据表选择")
        table_layout = QHBoxLayout(table_group)

        table_layout.addWidget(QLabel("字段映射数据表："))
        self.table_combo = QComboBox()
        self.table_combo.addItems(["试验问题表", "原始记录表"])
        self.table_combo.setCurrentText(self.current_table)
        self.table_combo.currentTextChanged.connect(self.on_table_changed)
        table_layout.addWidget(self.table_combo)
        table_layout.addStretch()

        layout.addWidget(table_group)

        # 说明标签
        info_label = QLabel("配置Excel/CSV文件字段与系统字段的映射关系：")
        layout.addWidget(info_label)

        # 映射表格
        self.mapping_table = QTableWidget()
        self.mapping_table.setColumnCount(4)
        self.mapping_table.setHorizontalHeaderLabels(['启用', '源字段', '目标字段', '主键'])

        # 设置列宽
        header = self.mapping_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.resizeSection(0, 80)   # 启用列
        header.resizeSection(1, 300)  # 源字段列
        header.resizeSection(2, 400)  # 目标字段列
        header.resizeSection(3, 80)   # 主键列

        layout.addWidget(self.mapping_table)

        # 按钮布局
        button_layout = QHBoxLayout()

        self.add_row_btn = QPushButton("添加行")
        self.add_row_btn.clicked.connect(self.add_mapping_row)
        button_layout.addWidget(self.add_row_btn)

        self.delete_row_btn = QPushButton("删除行")
        self.delete_row_btn.clicked.connect(self.delete_mapping_row)
        button_layout.addWidget(self.delete_row_btn)

        button_layout.addStretch()

        self.load_config_btn = QPushButton("加载配置")
        self.load_config_btn.clicked.connect(self.load_mapping_config)
        button_layout.addWidget(self.load_config_btn)

        self.save_config_btn = QPushButton("保存配置")
        self.save_config_btn.clicked.connect(self.save_mapping_config)
        button_layout.addWidget(self.save_config_btn)

        self.save_as_config_btn = QPushButton("另存为配置")
        self.save_as_config_btn.clicked.connect(self.save_as_mapping_config)
        button_layout.addWidget(self.save_as_config_btn)

        self.reset_btn = QPushButton("重置配置")
        self.reset_btn.clicked.connect(self.reset_mapping)
        button_layout.addWidget(self.reset_btn)

        layout.addLayout(button_layout)

        # 对话框按钮
        dialog_buttons = QHBoxLayout()
        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        dialog_buttons.addStretch()
        dialog_buttons.addWidget(self.cancel_btn)
        dialog_buttons.addWidget(self.ok_btn)

        layout.addLayout(dialog_buttons)

    def on_table_changed(self, table_name):
        """数据表切换事件处理"""
        try:
            # 更新当前选择的数据表
            self.current_table = table_name

            # 重新获取目标字段列表
            self.target_fields = self._get_target_fields()

            # 更新映射表格中的目标字段下拉框
            self._update_target_field_combos()

            # 重新加载默认映射配置
            self.load_default_mapping()

        except Exception as e:
            logger.error(f"数据表切换失败: {e}")
            QMessageBox.critical(self, "错误", f"数据表切换失败: {str(e)}")

    def _update_target_field_combos(self):
        """更新映射表格中的目标字段下拉框"""
        try:
            for row in range(self.mapping_table.rowCount()):
                target_combo = self.mapping_table.cellWidget(row, 2)
                if target_combo and isinstance(target_combo, QComboBox):
                    # 保存当前选择的值
                    current_value = target_combo.currentText()

                    # 清空并重新填充选项
                    target_combo.clear()
                    target_combo.addItem("")  # 空选项
                    target_combo.addItems(self.target_fields)

                    # 尝试恢复之前的选择
                    if current_value in self.target_fields or current_value == "":
                        target_combo.setCurrentText(current_value)
                    else:
                        target_combo.setCurrentText("")

        except Exception as e:
            logger.error(f"更新目标字段下拉框失败: {e}")

    def _get_target_fields(self):
        """获取目标字段列表 - 根据当前选择的数据表返回相应字段"""
        try:
            if self.current_table == "试验问题表":
                return self._get_problem_table_fields()
            elif self.current_table == "原始记录表":
                return self._get_original_table_fields()
            else:
                # 默认返回试验问题表字段
                return self._get_problem_table_fields()
        except Exception as e:
            logger.error(f"获取目标字段列表失败: {e}")
            return []

    def _get_problem_table_fields(self):
        """获取试验问题表字段列表 - 返回所有字段而不仅仅是问题管控字段"""
        try:
            # 修改：获取试验问题表的所有字段，而不仅仅是问题管控字段
            # 首先尝试从问题表字段分组配置获取所有字段
            all_fields = self._get_all_problem_table_fields()
            if all_fields:
                return sorted(list(set(all_fields)))  # 去重并排序

            # 如果获取失败，使用备用方法
            return self._get_problem_table_fields_fallback()

        except Exception as e:
            logger.error(f"获取试验问题表字段列表失败: {e}")
            return self._get_problem_table_fields_fallback()

    def _get_all_problem_table_fields(self):
        """获取试验问题表的所有字段（包括单独列字段、固定字段、可变字段）"""
        try:
            all_fields = []

            # 方法1：从问题表字段分组配置文件获取
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                     "config", "问题表字段分组.json")

            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    field_groups = json.load(f)

                # 合并所有分组的字段
                for group_name in ["individual_fields", "fixed_fields", "variable_fields"]:
                    if group_name in field_groups:
                        all_fields.extend(field_groups[group_name])

                if all_fields:
                    return all_fields

            # 方法2：从实际的JSON字段文件获取
            field_files = [
                "问题表单独列字段.json",
                "问题表固定字段.json",
                "问题表可变字段.json"
            ]

            for filename in field_files:
                file_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                       "config", filename)
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                            if data and isinstance(data, list) and len(data) > 0:
                                # 从JSON数组的第一个对象提取字段名
                                all_fields.extend(list(data[0].keys()))
                    except Exception as e:
                        logger.warning(f"读取字段文件 {filename} 失败: {e}")

            return all_fields

        except Exception as e:
            logger.error(f"获取试验问题表所有字段失败: {e}")
            return []

    def _get_problem_table_fields_fallback(self):
        """获取试验问题表字段的备用方法 - 返回默认的完整字段列表"""
        # 返回试验问题表的默认完整字段列表
        return [
            # 单独列字段
            "id", "试验类型", "样车编号", "问题编号_QTM", "问题编号_DTX", "问题描述_QTM_DTS_DTM",
            "图片名称", "图片路径", "图片链接", "纬度", "经度", "测试软件版本", "问题日期_QTM_DTS_DTM",
            "提出人", "提出人员工编号_QTM_DTM", "合并问题单号", "修改时间",

            # 固定字段
            "市场TOP10问题_DTS", "高感知_探测度_DTS", "高感知_频度_DTS", "高感知_影响度_DTS",
            "用户高感知结果", "严重度_QTM_DTS_DTM", "行车问题分类", "泊车问题分类",
            "复测场景编号/车位号", "复测情况", "指标采集特殊标签", "接管类型", "场景类型",
            "功能模式", "道路类型", "问题发生位置", "问题进展详情", "根本原因", "问题根因类别",
            "永久措施", "回归测试版本", "回归截止日期", "问题状态", "问题关闭方式",
            "问题价值标签", "问题价值简述",

            # 可变字段
            "平台迭代", "智驾平台", "项目名称_QTM", "动力系统_QTM", "问题来源_QTM",
            "问题属性_QTM", "故障部位_QTM", "故障模式_QTM", "牵头（处理）人工号_QTM_DTS_DTM",
            "项目副总师工号_QTM", "建议关闭日期_QTM_DTS_DTM", "问题分类_QTM", "问题类型_DTS",
            "复现概率_DTS _DTM", "下一环节_DTS", "下一环节处理人名称_DTS", "优先级_DTM",
            "发现活动_DTM", "发现阶段_DTM", "所属控制器", "责任专业", "行政单位"
        ]

    def _get_problem_table_fields_original(self):
        """原始的获取问题管控字段的方法 - 保留用于后续开发需要"""
        # 注释：这是原始的方法，仅返回问题管控字段分组的字段
        # 保留此代码是为了后续开发可能需要恢复到仅显示问题管控字段的功能
        try:
            # 从问题管控字段分组配置中获取"问题管控"分组的字段
            config_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                     "config", "problem_form_groups.json")

            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    groups_config = json.load(f)

                # 查找"问题管控"分组的字段
                groups = groups_config.get('groups', [])
                for group in groups:
                    if group.get('name') == '问题管控':
                        fields = group.get('fields', [])
                        return sorted(list(set(fields)))  # 去重并排序

                # 如果没有找到"问题管控"分组，返回默认字段列表
                return ['问题编号_QTM', '问题进展详情', '根本原因', '问题根因类别', '永久措施', '问题状态']
            else:
                # 如果配置文件不存在，返回默认字段列表
                return ['问题编号_QTM', '问题进展详情', '根本原因', '问题根因类别', '永久措施', '问题状态']

        except Exception as e:
            logger.error(f"获取问题管控字段列表失败: {e}")
            return ['问题编号_QTM', '问题进展详情', '根本原因', '问题根因类别', '永久措施', '问题状态']

    def _get_original_table_fields(self):
        """获取原始记录表字段列表"""
        try:
            # 原始记录表的字段分组
            field_groups = {
                "单独列字段": [
                    "id", "样车编号", "问题简述", "问题描述_QTM_DTS_DTM",
                    "纬度", "经度", "测试软件版本", "问题日期_QTM_DTS_DTM",
                    "具体时间", "问题提出人", "问题有效性"
                ],
                "固定字段": [
                    "道路类型", "功能模式", "位置", "行车问题分类", "泊车问题分类",
                    "接管类型", "日志数据批次", "dat数据批次", "dat数据",
                    "严重度_QTM_DTS_DTM", "场景类型", "车速"
                ],
                "可变字段": [
                    "数据来源", "测试环境", "天气条件", "路面状况", "交通状况",
                    "测试人员", "复现次数", "问题等级", "影响范围", "紧急程度",
                    "处理状态", "分配给", "预计完成时间", "实际完成时间",
                    "验证结果", "备注"
                ]
            }

            # 合并所有字段并排序
            all_fields = []
            for group_fields in field_groups.values():
                all_fields.extend(group_fields)

            return sorted(list(set(all_fields)))  # 去重并排序

        except Exception as e:
            logger.error(f"获取原始记录表字段失败: {e}")
            return ["id", "样车编号", "问题简述", "问题描述_QTM_DTS_DTM"]

    def _get_default_config_path(self):
        """获取默认配置文件路径 - 根据当前数据表选择不同的配置文件"""
        config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "config")

        if self.current_table == "试验问题表":
            return os.path.join(config_dir, "field_mapping_defaults.json")
        elif self.current_table == "原始记录表":
            return os.path.join(config_dir, "original_field_mapping_defaults.json")
        else:
            return os.path.join(config_dir, "field_mapping_defaults.json")

    def add_mapping_row(self):
        """添加映射行"""
        row_count = self.mapping_table.rowCount()
        self.mapping_table.insertRow(row_count)

        # 启用复选框
        enable_checkbox = QCheckBox()
        enable_checkbox.setChecked(True)
        self.mapping_table.setCellWidget(row_count, 0, enable_checkbox)

        # 源字段输入框
        source_field_edit = QLineEdit()
        self.mapping_table.setCellWidget(row_count, 1, source_field_edit)

        # 目标字段下拉框 - 使用可搜索的ComboBox
        target_combo = SearchableComboBox()
        target_combo.addItem("")  # 添加空选项
        target_combo.addItems(self.target_fields)
        self.mapping_table.setCellWidget(row_count, 2, target_combo)

        # 主键复选框
        primary_key_checkbox = QCheckBox()
        self.mapping_table.setCellWidget(row_count, 3, primary_key_checkbox)

    def delete_mapping_row(self):
        """删除选中的映射行"""
        current_row = self.mapping_table.currentRow()
        if current_row >= 0:
            self.mapping_table.removeRow(current_row)

    def get_mapping_config(self):
        """获取当前映射配置"""
        mappings = []
        for row in range(self.mapping_table.rowCount()):
            enable_checkbox = self.mapping_table.cellWidget(row, 0)
            source_field_edit = self.mapping_table.cellWidget(row, 1)
            target_combo = self.mapping_table.cellWidget(row, 2)
            primary_key_checkbox = self.mapping_table.cellWidget(row, 3)

            if enable_checkbox and source_field_edit and target_combo:
                mapping = {
                    'enabled': enable_checkbox.isChecked(),
                    'source_field': source_field_edit.text().strip(),
                    'target_field': target_combo.currentText().strip(),
                    'is_primary_key': primary_key_checkbox.isChecked() if primary_key_checkbox else False
                }
                mappings.append(mapping)

        return mappings

    def get_enabled_mappings(self):
        """获取启用的映射配置"""
        mappings = []
        for row in range(self.mapping_table.rowCount()):
            enable_checkbox = self.mapping_table.cellWidget(row, 0)
            source_field_edit = self.mapping_table.cellWidget(row, 1)
            target_combo = self.mapping_table.cellWidget(row, 2)
            primary_key_checkbox = self.mapping_table.cellWidget(row, 3)

            if enable_checkbox and source_field_edit and target_combo:
                # 只返回启用的映射
                if enable_checkbox.isChecked():
                    mapping = {
                        'enabled': True,
                        'source_field': source_field_edit.text().strip(),
                        'target_field': target_combo.currentText().strip(),
                        'is_primary_key': primary_key_checkbox.isChecked() if primary_key_checkbox else False
                    }
                    mappings.append(mapping)

        return mappings

    def set_mapping_config(self, mappings):
        """设置映射配置"""
        self.mapping_table.setRowCount(0)

        for mapping in mappings:
            row_count = self.mapping_table.rowCount()
            self.mapping_table.insertRow(row_count)

            # 启用复选框
            enable_checkbox = QCheckBox()
            enable_checkbox.setChecked(mapping.get('enabled', True))
            self.mapping_table.setCellWidget(row_count, 0, enable_checkbox)

            # 源字段输入框
            source_field_edit = QLineEdit()
            source_field_edit.setText(mapping.get('source_field', ''))
            self.mapping_table.setCellWidget(row_count, 1, source_field_edit)

            # 目标字段下拉框 - 使用可搜索的ComboBox
            target_combo = SearchableComboBox()
            target_combo.addItem("")  # 添加空选项
            target_combo.addItems(self.target_fields)
            target_field = mapping.get('target_field', '')
            if target_field:
                # 查找目标字段在下拉框中的索引
                index = target_combo.findText(target_field)
                if index >= 0:
                    target_combo.setCurrentIndex(index)
                else:
                    # 如果找不到，直接设置文本
                    target_combo.setCurrentText(target_field)
            self.mapping_table.setCellWidget(row_count, 2, target_combo)

            # 主键复选框
            primary_key_checkbox = QCheckBox()
            primary_key_checkbox.setChecked(mapping.get('is_primary_key', False))
            self.mapping_table.setCellWidget(row_count, 3, primary_key_checkbox)

    def load_mapping_config(self):
        """加载映射配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载映射配置", "", "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 检查配置格式
                if isinstance(config, list):
                    mappings = config
                elif isinstance(config, dict) and 'mappings' in config:
                    mappings = config['mappings']
                else:
                    QMessageBox.warning(self, "警告", "配置文件格式不正确")
                    return

                self.set_mapping_config(mappings)
                QMessageBox.information(self, "成功", "映射配置加载成功")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"加载配置失败: {str(e)}")

    def save_mapping_config(self):
        """保存映射配置到默认文件"""
        try:
            mappings = self.get_mapping_config()
            config = {
                'table': self.current_table,
                'mappings': mappings
            }

            # 确保配置目录存在
            config_dir = os.path.dirname(self.default_config_path)
            os.makedirs(config_dir, exist_ok=True)

            with open(self.default_config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            QMessageBox.information(self, "成功", f"映射配置已保存到: {self.default_config_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")

    def save_as_mapping_config(self):
        """另存为映射配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存映射配置", f"{self.current_table}_field_mapping.json", "JSON文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            try:
                mappings = self.get_mapping_config()
                config = {
                    'table': self.current_table,
                    'mappings': mappings
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "成功", f"映射配置已保存到: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存配置失败: {str(e)}")

    def load_default_mapping(self):
        """加载默认映射配置"""
        try:
            if os.path.exists(self.default_config_path):
                with open(self.default_config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)

                # 检查配置格式
                if isinstance(config, list):
                    mappings = config
                elif isinstance(config, dict) and 'mappings' in config:
                    mappings = config['mappings']
                else:
                    mappings = self._get_hardcoded_default_mappings()

                self.set_mapping_config(mappings)
            else:
                # 使用硬编码的默认映射
                default_mappings = self._get_hardcoded_default_mappings()
                self.set_mapping_config(default_mappings)

        except Exception as e:
            logger.error(f"加载默认映射配置失败: {e}")
            # 使用硬编码的默认映射作为后备
            default_mappings = self._get_hardcoded_default_mappings()
            self.set_mapping_config(default_mappings)

    def _get_hardcoded_default_mappings(self):
        """获取硬编码的默认映射配置 - 根据当前数据表返回不同的默认配置"""
        if self.current_table == "试验问题表":
            return [
                {'enabled': True, 'source_field': '样车编号', 'target_field': '样车编号', 'is_primary_key': False},
                {'enabled': True, 'source_field': '问题简述', 'target_field': '问题简述', 'is_primary_key': False},
                {'enabled': True, 'source_field': '问题描述', 'target_field': '问题描述', 'is_primary_key': False},
                {'enabled': True, 'source_field': '问题分类', 'target_field': '问题分类', 'is_primary_key': False},
                {'enabled': True, 'source_field': '严重程度', 'target_field': '严重程度', 'is_primary_key': False},
                {'enabled': True, 'source_field': '发现阶段', 'target_field': '发现阶段', 'is_primary_key': False},
                {'enabled': True, 'source_field': '责任部门', 'target_field': '责任部门', 'is_primary_key': False},
                {'enabled': True, 'source_field': '状态', 'target_field': '状态', 'is_primary_key': False},
                {'enabled': True, 'source_field': '发现日期', 'target_field': '发现日期', 'is_primary_key': False},
                {'enabled': True, 'source_field': '计划完成日期', 'target_field': '计划完成日期', 'is_primary_key': False},
                {'enabled': True, 'source_field': '实际完成日期', 'target_field': '实际完成日期', 'is_primary_key': False},
                {'enabled': True, 'source_field': '备注', 'target_field': '备注', 'is_primary_key': False}
            ]
        elif self.current_table == "原始记录表":
            return [
                {'enabled': True, 'source_field': '样车编号', 'target_field': '样车编号', 'is_primary_key': False},
                {'enabled': True, 'source_field': '问题简述', 'target_field': '问题简述', 'is_primary_key': False},
                {'enabled': True, 'source_field': '问题描述', 'target_field': '问题描述_QTM_DTS_DTM', 'is_primary_key': False},
                {'enabled': True, 'source_field': '记录日期', 'target_field': '记录日期', 'is_primary_key': False},
                {'enabled': True, 'source_field': '记录人', 'target_field': '记录人', 'is_primary_key': False},
                {'enabled': True, 'source_field': '备注', 'target_field': '备注', 'is_primary_key': False}
            ]
        else:
            # 默认返回试验问题表的配置
            return [
                {'enabled': True, 'source_field': '样车编号', 'target_field': '样车编号', 'is_primary_key': False},
                {'enabled': True, 'source_field': '问题简述', 'target_field': '问题简述', 'is_primary_key': False},
                {'enabled': True, 'source_field': '问题描述', 'target_field': '问题描述', 'is_primary_key': False}
            ]

    def reset_mapping(self):
        """重置映射配置"""
        reply = QMessageBox.question(
            self, "确认重置", "确定要重置映射配置吗？这将清除所有当前配置。",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.mapping_table.setRowCount(0)
            default_mappings = self._get_hardcoded_default_mappings()
            self.set_mapping_config(default_mappings)


class TransferFieldMappingDialog(QDialog):
    """传递字段映射配置对话框"""

    def __init__(self, parent=None, main_window=None):
        super().__init__(parent)
        self.main_window = main_window
        self.setWindowTitle("传递字段映射配置")
        self.setMinimumSize(1400, 900)
        self.setModal(True)

        # 当前选择的源数据表和目标数据表
        self.source_table = "试验问题表"  # 默认源数据表
        self.target_table = "原始记录表"  # 默认目标数据表

        # 设置当前表属性，用于配置文件路径选择
        self.current_table = self.source_table

        # 获取字段列表
        self.source_fields = self._get_table_fields(self.source_table)
        self.target_fields = self._get_table_fields(self.target_table)

        # 获取默认配置文件路径
        self.default_config_path = self._get_default_config_path()

        self.init_ui()
        self.load_default_mapping()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 数据表选择区域
        table_group = QGroupBox("数据表选择")
        table_layout = QHBoxLayout(table_group)

        # 源数据表选择
        table_layout.addWidget(QLabel("源数据表："))
        self.source_table_combo = QComboBox()
        self.source_table_combo.addItems(["试验问题表", "原始记录表"])
        self.source_table_combo.setCurrentText(self.source_table)
        self.source_table_combo.currentTextChanged.connect(self.on_source_table_changed)
        table_layout.addWidget(self.source_table_combo)

        table_layout.addWidget(QLabel("    "))  # 间距

        # 目标数据表选择
        table_layout.addWidget(QLabel("目标数据表："))
        self.target_table_combo = QComboBox()
        self.target_table_combo.addItems(["试验问题表", "原始记录表"])
        self.target_table_combo.setCurrentText(self.target_table)
        self.target_table_combo.currentTextChanged.connect(self.on_target_table_changed)
        table_layout.addWidget(self.target_table_combo)

        table_layout.addStretch()

        layout.addWidget(table_group)

        # 说明标签
        info_label = QLabel("配置源数据表字段与目标数据表字段的传递映射关系：")
        layout.addWidget(info_label)

        # 映射表格
        self.mapping_table = QTableWidget()
        self.mapping_table.setColumnCount(4)
        self.mapping_table.setHorizontalHeaderLabels(['启用', '源字段', '目标字段', '主键'])

        # 设置列宽
        header = self.mapping_table.horizontalHeader()
        header.setStretchLastSection(False)
        header.resizeSection(0, 80)   # 启用列
        header.resizeSection(1, 400)  # 源字段列
        header.resizeSection(2, 400)  # 目标字段列
        header.resizeSection(3, 80)   # 主键列

        layout.addWidget(self.mapping_table)

        # 按钮布局
        button_layout = QHBoxLayout()

        self.add_row_btn = QPushButton("添加行")
        self.add_row_btn.clicked.connect(self.add_mapping_row)
        button_layout.addWidget(self.add_row_btn)

        self.delete_row_btn = QPushButton("删除行")
        self.delete_row_btn.clicked.connect(self.delete_mapping_row)
        button_layout.addWidget(self.delete_row_btn)

        button_layout.addStretch()

        self.load_config_btn = QPushButton("加载配置")
        self.load_config_btn.clicked.connect(self.load_mapping_config)
        button_layout.addWidget(self.load_config_btn)

        self.save_config_btn = QPushButton("保存配置")
        self.save_config_btn.clicked.connect(self.save_mapping_config)
        button_layout.addWidget(self.save_config_btn)

        self.save_as_config_btn = QPushButton("另存为配置")
        self.save_as_config_btn.clicked.connect(self.save_as_mapping_config)
        button_layout.addWidget(self.save_as_config_btn)

        self.reset_config_btn = QPushButton("重置配置")
        self.reset_config_btn.clicked.connect(self.reset_mapping_config)
        button_layout.addWidget(self.reset_config_btn)

        layout.addLayout(button_layout)

        # 对话框按钮
        dialog_button_layout = QHBoxLayout()
        dialog_button_layout.addStretch()

        self.ok_btn = QPushButton("确定")
        self.ok_btn.clicked.connect(self.accept)
        dialog_button_layout.addWidget(self.ok_btn)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)
        dialog_button_layout.addWidget(self.cancel_btn)

        layout.addLayout(dialog_button_layout)

    def _get_table_fields(self, table_name):
        """根据数据表名称获取字段列表"""
        if table_name == "试验问题表":
            return self._get_problem_table_fields()
        elif table_name == "原始记录表":
            return self._get_original_table_fields()
        else:
            return []

    def _get_problem_table_fields(self):
        """获取试验问题表字段列表（仅问题录入中的字段）"""
        try:
            # 从问题录入分组中获取字段
            config_path = os.path.join("config", "problem_form_groups.json")
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    groups = config_data.get("groups", [])
                    for group in groups:
                        if group.get("name") == "问题录入":
                            return sorted(group.get("fields", []))

            # 如果配置文件不存在或没有找到问题录入分组，返回默认字段
            return [
                "样车编号", "市场TOP10问题_DTS", "高感知_影响度_DTS", "高感知_探测度_DTS",
                "高感知_频度_DTS", "问题描述_QTM_DTS_DTM", "图片名称", "图片路径",
                "严重度_QTM_DTS_DTM", "行车问题分类", "指标采集特殊标签", "接管类型",
                "场景类型", "纬度", "经度", "功能模式", "道路类型", "问题发生位置",
                "测试软件版本", "问题日期_QTM_DTS_DTM", "提出人", "提出人员工编号_QTM_DTM"
            ]

        except Exception as e:
            logger.error(f"获取试验问题表字段失败: {e}")
            return ["样车编号", "问题描述_QTM_DTS_DTM", "测试软件版本", "问题日期_QTM_DTS_DTM"]

    def _get_original_table_fields(self):
        """获取原始记录表字段列表"""
        try:
            # 原始记录表的字段分组
            field_groups = {
                "单独列字段": [
                    "id", "样车编号", "问题简述", "问题描述_QTM_DTS_DTM",
                    "纬度", "经度", "测试软件版本", "问题日期_QTM_DTS_DTM",
                    "具体时间", "问题提出人", "问题有效性"
                ],
                "固定字段": [
                    "道路类型", "功能模式", "位置", "行车问题分类", "泊车问题分类",
                    "接管类型", "日志数据批次", "dat数据批次", "dat数据",
                    "严重度_QTM_DTS_DTM", "场景类型", "车速"
                ],
                "可变字段": [
                    "数据来源", "测试环境", "天气条件", "路面状况", "交通状况",
                    "测试人员", "复现次数", "问题等级", "影响范围", "紧急程度",
                    "处理状态", "分配给", "预计完成时间", "实际完成时间",
                    "验证结果", "备注"
                ]
            }

            # 合并所有字段并排序
            all_fields = []
            for group_fields in field_groups.values():
                all_fields.extend(group_fields)

            return sorted(list(set(all_fields)))  # 去重并排序

        except Exception as e:
            logger.error(f"获取原始记录表字段失败: {e}")
            return ["id", "样车编号", "问题简述", "问题描述_QTM_DTS_DTM"]

    def _get_default_config_path(self):
        """获取默认配置文件路径"""
        source_name = getattr(self, 'source_table', '试验问题表').replace("表", "")
        target_name = getattr(self, 'target_table', '原始记录表').replace("表", "")
        filename = f"传递字段映射_{source_name}_到_{target_name}.json"
        return os.path.join("config", filename)

    def on_source_table_changed(self, table_name):
        """源数据表改变事件处理"""
        self.source_table = table_name
        self.source_fields = self._get_table_fields(table_name)
        self._update_source_field_combos()
        self.default_config_path = self._get_default_config_path()
        self.load_default_mapping()

    def on_target_table_changed(self, table_name):
        """目标数据表改变事件处理"""
        self.target_table = table_name
        self.target_fields = self._get_table_fields(table_name)
        self._update_target_field_combos()
        self.default_config_path = self._get_default_config_path()
        self.load_default_mapping()

    def _update_source_field_combos(self):
        """更新所有源字段下拉框"""
        for row in range(self.mapping_table.rowCount()):
            source_combo = self.mapping_table.cellWidget(row, 1)
            if isinstance(source_combo, QComboBox):
                current_text = source_combo.currentText()
                source_combo.clear()
                source_combo.addItems([''] + self.source_fields)
                # 尝试恢复之前的选择
                index = source_combo.findText(current_text)
                if index >= 0:
                    source_combo.setCurrentIndex(index)
                else:
                    # 如果是SearchableComboBox，直接设置文本
                    source_combo.setCurrentText(current_text)

    def _update_target_field_combos(self):
        """更新所有目标字段下拉框"""
        for row in range(self.mapping_table.rowCount()):
            target_combo = self.mapping_table.cellWidget(row, 2)
            if isinstance(target_combo, QComboBox):
                current_text = target_combo.currentText()
                target_combo.clear()
                target_combo.addItems([''] + self.target_fields)
                # 尝试恢复之前的选择
                index = target_combo.findText(current_text)
                if index >= 0:
                    target_combo.setCurrentIndex(index)
                else:
                    # 如果是SearchableComboBox，直接设置文本
                    target_combo.setCurrentText(current_text)

    def add_mapping_row(self):
        """添加映射行"""
        row_count = self.mapping_table.rowCount()
        self.mapping_table.insertRow(row_count)

        # 启用复选框 - 居中对齐
        enable_checkbox = QCheckBox()
        enable_checkbox.setChecked(True)
        enable_widget = QWidget()
        enable_layout = QHBoxLayout(enable_widget)
        enable_layout.addWidget(enable_checkbox)
        enable_layout.setAlignment(Qt.AlignCenter)
        enable_layout.setContentsMargins(0, 0, 0, 0)
        self.mapping_table.setCellWidget(row_count, 0, enable_widget)

        # 源字段下拉框 - 使用可搜索的ComboBox
        source_field_combo = SearchableComboBox()
        source_field_combo.addItems([''] + self.source_fields)
        self.mapping_table.setCellWidget(row_count, 1, source_field_combo)

        # 目标字段下拉框 - 使用可搜索的ComboBox
        target_field_combo = SearchableComboBox()
        target_field_combo.addItems([''] + self.target_fields)
        self.mapping_table.setCellWidget(row_count, 2, target_field_combo)

        # 主键复选框 - 居中对齐
        primary_key_checkbox = QCheckBox()
        primary_key_checkbox.setChecked(False)
        primary_key_widget = QWidget()
        primary_key_layout = QHBoxLayout(primary_key_widget)
        primary_key_layout.addWidget(primary_key_checkbox)
        primary_key_layout.setAlignment(Qt.AlignCenter)
        primary_key_layout.setContentsMargins(0, 0, 0, 0)
        self.mapping_table.setCellWidget(row_count, 3, primary_key_widget)

    def delete_mapping_row(self):
        """删除选中的映射行"""
        current_row = self.mapping_table.currentRow()
        if current_row >= 0:
            self.mapping_table.removeRow(current_row)
        else:
            QMessageBox.information(self, "提示", "请先选择要删除的行")

    def load_default_mapping(self):
        """加载默认映射配置"""
        try:
            if os.path.exists(self.default_config_path):
                with open(self.default_config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    self.set_mapping_config(config_data.get('default_mappings', []))
            else:
                # 如果配置文件不存在，创建一些默认映射
                default_mappings = self._get_hardcoded_default_mappings()
                self.set_mapping_config(default_mappings)
        except Exception as e:
            logger.error(f"加载默认映射配置失败: {e}")
            QMessageBox.warning(self, "警告", f"加载默认映射配置失败: {e}")

    def _get_hardcoded_default_mappings(self):
        """获取硬编码的默认映射配置"""
        if self.source_table == "试验问题表" and self.target_table == "原始记录表":
            return [
                {"enabled": True, "source_field": "样车编号", "target_field": "样车编号", "primary_key": True},
                {"enabled": True, "source_field": "问题描述_QTM_DTS_DTM", "target_field": "问题描述_QTM_DTS_DTM", "primary_key": False},
                {"enabled": True, "source_field": "测试软件版本", "target_field": "测试软件版本", "primary_key": False},
                {"enabled": True, "source_field": "问题日期_QTM_DTS_DTM", "target_field": "问题日期_QTM_DTS_DTM", "primary_key": False},
                {"enabled": True, "source_field": "纬度", "target_field": "纬度", "primary_key": False},
                {"enabled": True, "source_field": "经度", "target_field": "经度", "primary_key": False},
                {"enabled": True, "source_field": "提出人", "target_field": "问题提出人", "primary_key": False},
            ]
        elif self.source_table == "原始记录表" and self.target_table == "试验问题表":
            return [
                {"enabled": True, "source_field": "样车编号", "target_field": "样车编号", "primary_key": True},
                {"enabled": True, "source_field": "问题描述_QTM_DTS_DTM", "target_field": "问题描述_QTM_DTS_DTM", "primary_key": False},
                {"enabled": True, "source_field": "测试软件版本", "target_field": "测试软件版本", "primary_key": False},
                {"enabled": True, "source_field": "问题日期_QTM_DTS_DTM", "target_field": "问题日期_QTM_DTS_DTM", "primary_key": False},
                {"enabled": True, "source_field": "纬度", "target_field": "纬度", "primary_key": False},
                {"enabled": True, "source_field": "经度", "target_field": "经度", "primary_key": False},
                {"enabled": True, "source_field": "问题提出人", "target_field": "提出人", "primary_key": False},
            ]
        else:
            return []

    def set_mapping_config(self, mappings):
        """设置映射配置"""
        # 清空现有行
        self.mapping_table.setRowCount(0)

        # 添加映射行
        for mapping in mappings:
            self.add_mapping_row()
            row = self.mapping_table.rowCount() - 1

            # 设置启用状态
            enable_widget = self.mapping_table.cellWidget(row, 0)
            if enable_widget:
                checkbox = enable_widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(mapping.get('enabled', True))

            # 设置源字段
            source_combo = self.mapping_table.cellWidget(row, 1)
            if isinstance(source_combo, QComboBox):
                source_field = mapping.get('source_field', '')
                index = source_combo.findText(source_field)
                if index >= 0:
                    source_combo.setCurrentIndex(index)
                else:
                    # 如果是SearchableComboBox，直接设置文本
                    source_combo.setCurrentText(source_field)

            # 设置目标字段
            target_combo = self.mapping_table.cellWidget(row, 2)
            if isinstance(target_combo, QComboBox):
                target_field = mapping.get('target_field', '')
                index = target_combo.findText(target_field)
                if index >= 0:
                    target_combo.setCurrentIndex(index)
                else:
                    # 如果是SearchableComboBox，直接设置文本
                    target_combo.setCurrentText(target_field)

            # 设置主键状态
            primary_key_widget = self.mapping_table.cellWidget(row, 3)
            if primary_key_widget:
                checkbox = primary_key_widget.findChild(QCheckBox)
                if checkbox:
                    checkbox.setChecked(mapping.get('primary_key', False))

    def get_enabled_mappings(self):
        """获取启用的映射配置"""
        mappings = []
        for row in range(self.mapping_table.rowCount()):
            # 检查是否启用
            enable_widget = self.mapping_table.cellWidget(row, 0)
            if enable_widget:
                checkbox = enable_widget.findChild(QCheckBox)
                if not checkbox or not checkbox.isChecked():
                    continue

            # 获取源字段
            source_combo = self.mapping_table.cellWidget(row, 1)
            source_field = source_combo.currentText() if isinstance(source_combo, QComboBox) else ''

            # 获取目标字段
            target_combo = self.mapping_table.cellWidget(row, 2)
            target_field = target_combo.currentText() if isinstance(target_combo, QComboBox) else ''

            # 获取主键状态
            primary_key_widget = self.mapping_table.cellWidget(row, 3)
            is_primary_key = False
            if primary_key_widget:
                checkbox = primary_key_widget.findChild(QCheckBox)
                if checkbox:
                    is_primary_key = checkbox.isChecked()

            if source_field and target_field:
                mappings.append({
                    'enabled': True,
                    'source_field': source_field,
                    'target_field': target_field,
                    'primary_key': is_primary_key
                })

        return mappings

    def get_all_mappings(self):
        """获取所有映射配置（包括未启用的）"""
        mappings = []
        for row in range(self.mapping_table.rowCount()):
            # 检查是否启用
            enable_widget = self.mapping_table.cellWidget(row, 0)
            enabled = True
            if enable_widget:
                checkbox = enable_widget.findChild(QCheckBox)
                if checkbox:
                    enabled = checkbox.isChecked()

            # 获取源字段
            source_combo = self.mapping_table.cellWidget(row, 1)
            source_field = source_combo.currentText() if isinstance(source_combo, QComboBox) else ''

            # 获取目标字段
            target_combo = self.mapping_table.cellWidget(row, 2)
            target_field = target_combo.currentText() if isinstance(target_combo, QComboBox) else ''

            # 获取主键状态
            primary_key_widget = self.mapping_table.cellWidget(row, 3)
            is_primary_key = False
            if primary_key_widget:
                checkbox = primary_key_widget.findChild(QCheckBox)
                if checkbox:
                    is_primary_key = checkbox.isChecked()

            mappings.append({
                'enabled': enabled,
                'source_field': source_field,
                'target_field': target_field,
                'primary_key': is_primary_key
            })

        return mappings

    def load_mapping_config(self):
        """从文件加载映射配置"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载传递字段映射配置", "config", "JSON文件 (*.json)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    mappings = config_data.get('default_mappings', [])
                    self.set_mapping_config(mappings)
                    QMessageBox.information(self, "成功", "映射配置加载成功")
            except Exception as e:
                logger.error(f"加载映射配置失败: {e}")
                QMessageBox.warning(self, "错误", f"加载映射配置失败: {e}")

    def save_mapping_config(self):
        """保存映射配置到默认文件"""
        try:
            # 确保config目录存在
            os.makedirs("config", exist_ok=True)

            config_data = {
                'source_table': self.source_table,
                'target_table': self.target_table,
                'default_mappings': self.get_all_mappings(),
                'common_primary_keys': [
                    "id", "ID", "编号", "样车编号", "问题编号_QTM", "问题编号_DTX"
                ]
            }

            with open(self.default_config_path, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            QMessageBox.information(self, "成功", f"映射配置已保存到: {self.default_config_path}")
        except Exception as e:
            logger.error(f"保存映射配置失败: {e}")
            QMessageBox.warning(self, "错误", f"保存映射配置失败: {e}")

    def save_as_mapping_config(self):
        """另存为映射配置"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "另存为传递字段映射配置", "config", "JSON文件 (*.json)"
        )

        if file_path:
            try:
                config_data = {
                    'source_table': self.source_table,
                    'target_table': self.target_table,
                    'default_mappings': self.get_all_mappings(),
                    'common_primary_keys': [
                        "id", "ID", "编号", "样车编号", "问题编号_QTM", "问题编号_DTX"
                    ]
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(config_data, f, ensure_ascii=False, indent=2)

                QMessageBox.information(self, "成功", f"映射配置已保存到: {file_path}")
            except Exception as e:
                logger.error(f"另存为映射配置失败: {e}")
                QMessageBox.warning(self, "错误", f"另存为映射配置失败: {e}")

    def reset_mapping_config(self):
        """重置映射配置"""
        reply = QMessageBox.question(
            self, "确认重置", "确定要重置映射配置吗？这将清除所有当前配置。",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.mapping_table.setRowCount(0)
            default_mappings = self._get_hardcoded_default_mappings()
            self.set_mapping_config(default_mappings)



class ImportDataDialog(QDialog):
    """数据导入对话框"""

    def __init__(self, parent=None, main_window=None):
        super().__init__(parent)
        self.main_window = main_window
        self.setWindowTitle("导入问题原始记录")
        self.setMinimumSize(1000, 700)
        self.setModal(True)

        # 🔧 优化：设置完整的窗口标志，确保显示所有标准窗口控制按钮
        # 包括最小化、最大化/还原、关闭按钮，与其他对话框保持一致
        from src.utils.window_utils import get_standard_dialog_flags
        self.setWindowFlags(get_standard_dialog_flags())

        # 数据存储
        self.file_path = None
        self.data_frame = None
        self.field_mappings = []

        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 目标表选择区域
        target_group = QGroupBox("目标表选择")
        target_layout = QHBoxLayout(target_group)

        target_layout.addWidget(QLabel("导入数据至："))
        self.target_table_combo = QComboBox()
        self.target_table_combo.addItems(["试验问题表", "原始记录表"])
        self.target_table_combo.setCurrentText("试验问题表")  # 默认选择试验问题表，保持向后兼容
        target_layout.addWidget(self.target_table_combo)
        target_layout.addStretch()

        layout.addWidget(target_group)

        # 文件选择区域
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)

        file_select_layout = QHBoxLayout()
        self.file_path_edit = QLineEdit()
        self.file_path_edit.setReadOnly(True)
        self.browse_btn = QPushButton("浏览...")
        self.browse_btn.clicked.connect(self.browse_file)

        file_select_layout.addWidget(QLabel("文件路径:"))
        file_select_layout.addWidget(self.file_path_edit)
        file_select_layout.addWidget(self.browse_btn)
        file_layout.addLayout(file_select_layout)

        # 工作表选择（仅Excel文件）
        sheet_layout = QHBoxLayout()
        self.sheet_combo = QComboBox()
        self.sheet_combo.currentTextChanged.connect(self.load_data_preview)

        sheet_layout.addWidget(QLabel("工作表:"))
        sheet_layout.addWidget(self.sheet_combo)
        sheet_layout.addStretch()
        file_layout.addLayout(sheet_layout)

        layout.addWidget(file_group)

        # 数据预览区域
        preview_group = QGroupBox("数据预览")
        preview_layout = QVBoxLayout(preview_group)

        self.preview_table = QTableWidget()
        self.preview_table.setEditTriggers(QTableWidget.NoEditTriggers)
        preview_layout.addWidget(self.preview_table)

        layout.addWidget(preview_group)

        # 字段映射区域
        mapping_group = QGroupBox("字段映射配置")
        mapping_layout = QHBoxLayout(mapping_group)

        self.config_mapping_btn = QPushButton("配置字段映射")
        self.config_mapping_btn.clicked.connect(self.configure_field_mapping)
        mapping_layout.addWidget(self.config_mapping_btn)

        self.mapping_status_label = QLabel("未配置字段映射")
        mapping_layout.addWidget(self.mapping_status_label)
        mapping_layout.addStretch()

        layout.addWidget(mapping_group)

        # 导入选项
        options_group = QGroupBox("导入选项")
        options_layout = QVBoxLayout(options_group)

        self.update_existing_checkbox = QCheckBox("更新现有记录（基于主键匹配）")
        self.update_existing_checkbox.setChecked(True)
        options_layout.addWidget(self.update_existing_checkbox)

        self.create_new_checkbox = QCheckBox("创建新记录（主键不匹配时）")
        self.create_new_checkbox.setChecked(True)
        options_layout.addWidget(self.create_new_checkbox)

        layout.addWidget(options_group)

        # 按钮区域
        button_layout = QHBoxLayout()

        self.import_btn = QPushButton("开始导入")
        self.import_btn.clicked.connect(self.start_import)
        self.import_btn.setEnabled(False)

        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.clicked.connect(self.reject)

        button_layout.addStretch()
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.import_btn)

        layout.addLayout(button_layout)

    def browse_file(self):
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择Excel/CSV文件", "",
            "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv);;所有文件 (*)"
        )

        if file_path:
            self.file_path = file_path
            self.file_path_edit.setText(file_path)
            self.load_file_info()

    def load_file_info(self):
        """加载文件信息"""
        if not self.file_path:
            return

        try:
            if pd is None:
                QMessageBox.critical(self, "错误", "pandas库未安装，无法处理Excel/CSV文件")
                return

            file_ext = os.path.splitext(self.file_path)[1].lower()

            if file_ext in ['.xlsx', '.xls']:
                # Excel文件，获取工作表列表
                excel_file = pd.ExcelFile(self.file_path)
                self.sheet_combo.clear()
                self.sheet_combo.addItems(excel_file.sheet_names)
                self.sheet_combo.setVisible(True)
                self.sheet_combo.parentWidget().setVisible(True)
            else:
                # CSV文件，隐藏工作表选择
                self.sheet_combo.setVisible(False)
                self.sheet_combo.parentWidget().setVisible(False)
                self.load_data_preview()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"加载文件失败: {str(e)}")

    def load_data_preview(self):
        """加载数据预览"""
        if not self.file_path:
            return

        try:
            if pd is None:
                return

            file_ext = os.path.splitext(self.file_path)[1].lower()

            if file_ext in ['.xlsx', '.xls']:
                sheet_name = self.sheet_combo.currentText()
                if not sheet_name:
                    return
                self.data_frame = pd.read_excel(self.file_path, sheet_name=sheet_name)
            else:
                # CSV文件，尝试自动检测编码
                encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
                for encoding in encodings:
                    try:
                        self.data_frame = pd.read_csv(self.file_path, encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise Exception("无法识别文件编码")

            # 显示预览数据（前10行）
            self.show_data_preview()
            self.import_btn.setEnabled(True)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"读取数据失败: {str(e)}")
            self.data_frame = None
            self.import_btn.setEnabled(False)

    def show_data_preview(self):
        """显示数据预览"""
        if self.data_frame is None:
            return

        # 获取前10行数据用于预览
        preview_data = self.data_frame.head(10)

        # 设置表格
        self.preview_table.setRowCount(len(preview_data))
        self.preview_table.setColumnCount(len(preview_data.columns))
        self.preview_table.setHorizontalHeaderLabels([str(col) for col in preview_data.columns])

        # 填充数据
        for row_idx, (_, row) in enumerate(preview_data.iterrows()):
            for col_idx, value in enumerate(row):
                item = QTableWidgetItem(str(value) if pd.notna(value) else "")
                self.preview_table.setItem(row_idx, col_idx, item)

        # 调整列宽
        self.preview_table.resizeColumnsToContents()

    def configure_field_mapping(self):
        """配置字段映射"""
        if self.data_frame is None:
            QMessageBox.warning(self, "提示", "请先选择并加载数据文件")
            return

        # 创建字段映射对话框
        mapping_dialog = FieldMappingDialog(self, self.main_window)

        # 如果已有映射配置，先加载
        if self.field_mappings:
            mapping_dialog.set_mapping_config(self.field_mappings)
        else:
            # 自动匹配字段名
            self.auto_match_fields(mapping_dialog)

        if mapping_dialog.exec_() == QDialog.Accepted:
            self.field_mappings = mapping_dialog.get_enabled_mappings()
            self.update_mapping_status()

    def auto_match_fields(self, mapping_dialog):
        """自动匹配字段名"""
        if self.data_frame is None:
            return

        source_fields = list(self.data_frame.columns)
        target_fields = mapping_dialog.target_fields

        # 清空现有映射
        mapping_dialog.mapping_table.setRowCount(0)

        # 尝试自动匹配
        auto_mappings = []
        for source_field in source_fields:
            # 寻找最佳匹配的目标字段
            best_match = None
            for target_field in target_fields:
                if source_field == target_field or source_field in target_field or target_field in source_field:
                    best_match = target_field
                    break

            auto_mappings.append({
                'enabled': best_match is not None,
                'source_field': source_field,
                'target_field': best_match or '',
                'is_primary_key': source_field.lower() in ['id', 'ID', '编号', '问题编号']
            })

        mapping_dialog.set_mapping_config(auto_mappings)

    def update_mapping_status(self):
        """更新映射状态显示"""
        if self.field_mappings:
            enabled_count = len(self.field_mappings)
            primary_keys = [m['source_field'] for m in self.field_mappings if m.get('is_primary_key', False)]

            status_text = f"已配置 {enabled_count} 个字段映射"
            if primary_keys:
                status_text += f"，主键: {', '.join(primary_keys)}"

            self.mapping_status_label.setText(status_text)
        else:
            self.mapping_status_label.setText("未配置字段映射")

    def start_import(self):
        """开始导入数据"""
        if self.data_frame is None:
            QMessageBox.warning(self, "提示", "请先选择并加载数据文件")
            return

        if not self.field_mappings:
            QMessageBox.warning(self, "提示", "请先配置字段映射")
            return

        # 验证主键配置
        primary_keys = [m['source_field'] for m in self.field_mappings if m.get('is_primary_key', False)]
        if not primary_keys:
            reply = QMessageBox.question(
                self, "确认导入",
                "未配置主键字段，所有数据将作为新记录导入。是否继续？",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            if reply != QMessageBox.Yes:
                return

        # 显示导入确认对话框
        self.show_import_confirmation()

    def show_import_confirmation(self):
        """显示导入确认对话框"""
        total_rows = len(self.data_frame)
        mapped_fields = len(self.field_mappings)
        primary_keys = [m['source_field'] for m in self.field_mappings if m.get('is_primary_key', False)]
        target_table = self.target_table_combo.currentText()

        message = f"""
导入确认信息：
• 目标数据表: {target_table}
• 数据文件: {os.path.basename(self.file_path)}
• 总记录数: {total_rows}
• 映射字段数: {mapped_fields}
• 主键字段: {', '.join(primary_keys) if primary_keys else '无'}
• 更新现有记录: {'是' if self.update_existing_checkbox.isChecked() else '否'}
• 创建新记录: {'是' if self.create_new_checkbox.isChecked() else '否'}

确定要开始导入吗？
        """

        reply = QMessageBox.question(
            self, "确认导入", message.strip(),
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.perform_import()

    def perform_import(self):
        """执行数据导入"""
        try:
            if ImportProcessor is None:
                QMessageBox.critical(self, "错误", "导入处理器未能加载")
                return

            # 根据目标表选择获取相应的数据模型
            target_table = self.target_table_combo.currentText()
            target_model = None

            if target_table == "试验问题表":
                # 检查问题数据模型是否初始化
                if hasattr(self.main_window, '_controller') and self.main_window._controller:
                    target_model = self.main_window._controller.problem_model
                elif hasattr(self.main_window, 'problem_model'):
                    target_model = self.main_window.problem_model

                if target_model is None:
                    QMessageBox.critical(self, "错误", "试验问题表数据模型未初始化")
                    return

            elif target_table == "原始记录表":
                # 检查原始记录表数据模型是否初始化
                if hasattr(self.main_window, 'original_table_manager') and self.main_window.original_table_manager:
                    # 从原始记录表管理器获取数据模型
                    target_model = self.main_window.original_table_manager.original_model
                else:
                    QMessageBox.critical(self, "错误", "原始记录表数据模型未初始化")
                    return

            # 创建导入处理器
            processor = ImportProcessor(target_model, self.main_window, target_table)

            # 验证字段映射
            validation_errors = processor.validate_field_mappings(self.field_mappings, self.data_frame)
            if validation_errors:
                error_msg = "字段映射验证失败:\n" + "\n".join(validation_errors)
                QMessageBox.critical(self, "验证失败", error_msg)
                return

            # 执行导入
            result = processor.process_import(
                self.data_frame,
                self.field_mappings,
                self.update_existing_checkbox.isChecked(),
                self.create_new_checkbox.isChecked()
            )

            # 生成并显示导入报告
            report = processor.generate_import_report(result)

            if result['error_count'] == 0:
                QMessageBox.information(self, "导入完成", report)
            else:
                # 如果有错误，提供导出错误报告的选项
                msg_box = QMessageBox(self)
                msg_box.setWindowTitle("导入完成（有错误）")
                msg_box.setText(report)
                msg_box.setIcon(QMessageBox.Warning)

                export_btn = msg_box.addButton("导出错误报告", QMessageBox.ActionRole)
                ok_btn = msg_box.addButton("确定", QMessageBox.AcceptRole)

                msg_box.exec_()

                if msg_box.clickedButton() == export_btn:
                    self.export_error_report(result['errors'])

            # 如果有成功导入的记录，刷新主窗口数据
            if result['success_count'] > 0:
                target_table = self.target_table_combo.currentText()
                if target_table == "试验问题表" and hasattr(self.main_window, 'load_problems'):
                    self.main_window.load_problems()
                elif target_table == "原始记录表" and hasattr(self.main_window, 'original_table_manager'):
                    # 刷新原始记录表数据
                    if hasattr(self.main_window.original_table_manager, 'refresh_data'):
                        self.main_window.original_table_manager.refresh_data()
                    elif hasattr(self.main_window.original_table_manager, 'load_data'):
                        self.main_window.original_table_manager.load_data()

            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "导入失败", f"数据导入过程中发生错误: {str(e)}")

    def get_import_data(self):
        """获取导入数据和配置"""
        return {
            'data_frame': self.data_frame,
            'field_mappings': self.field_mappings,
            'update_existing': self.update_existing_checkbox.isChecked(),
            'create_new': self.create_new_checkbox.isChecked(),
            'file_path': self.file_path,
            'target_table': self.target_table_combo.currentText()  # 添加目标表选择信息
        }

    def export_error_report(self, errors):
        """导出错误报告"""
        try:
            if not errors:
                QMessageBox.information(self, "提示", "没有错误需要导出")
                return

            # 选择保存文件
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出错误报告", "import_errors.txt",
                "文本文件 (*.txt);;CSV文件 (*.csv);;所有文件 (*)"
            )

            if file_path:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write("数据导入错误报告\n")
                    f.write("=" * 50 + "\n\n")

                    for i, error in enumerate(errors, 1):
                        f.write(f"{i}. 第 {error['row']} 行: {error['error']}\n")

                QMessageBox.information(self, "成功", f"错误报告已导出到: {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导出错误报告失败: {str(e)}")