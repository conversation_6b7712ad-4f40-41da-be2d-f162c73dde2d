{"version": 3, "file": "data_range1d.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/models/ranges/data_range1d.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,SAAS,EAAC,MAAM,cAAc,CAAA;AACtC,OAAO,KAAK,EAAC,QAAQ,EAAC,MAAM,uBAAuB,CAAA;AACnD,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,4BAA4B,CAAA;AAC5D,OAAO,EAAC,YAAY,EAAE,QAAQ,EAAC,yBAAkB;AACjD,OAAO,EAAC,IAAI,EAAC,yBAAkB;AAG/B,OAAO,KAAK,CAAC,8BAAuB;AAEpC,OAAO,KAAK,EAAC,IAAI,EAAC,MAAM,eAAe,CAAA;AAGvC,oBAAY,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;AACvB,oBAAY,MAAM,GAAG,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;AAExC,yBAAiB,WAAW,CAAC;IAC3B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,SAAS,CAAC,KAAK,GAAG;QACpC,KAAK,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACzB,GAAG,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACvB,aAAa,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QACjC,mBAAmB,EAAE,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAA;QAC7C,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QAC5B,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAA;QACnC,eAAe,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,CAAA;QAC1C,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;QAChC,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QAEjC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,KAAK,GAAG,MAAM,CAAC,CAAA;KACvC,CAAA;CACF;AAED,MAAM,WAAW,WAAY,SAAQ,WAAW,CAAC,KAAK;CAAG;AAEzD,qBAAa,WAAY,SAAQ,SAAS;IAC/B,UAAU,EAAE,WAAW,CAAC,KAAK,CAAA;gBAE1B,KAAK,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC;IAsB9C,SAAS,CAAC,cAAc,EAAE,MAAM,GAAG,IAAI,CAAA;IACvC,SAAS,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI,CAAA;IACrC,SAAS,CAAC,sBAAsB,EAAE,MAAM,CAAA;IACxC,SAAS,CAAC,4BAA4B,EAAE,YAAY,CAAA;IACpD,SAAS,CAAC,eAAe,EAAE,QAAQ,GAAG,IAAI,CAAA;IAC1C,SAAS,CAAC,wBAAwB,EAAE,MAAM,GAAG,IAAI,CAAA;IACjD,SAAS,CAAC,qBAAqB,EAAE,MAAM,CAAA;IAEvC,SAAS,CAAC,YAAY,EAAE,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;IAE9B,0BAA0B,EAAE,OAAO,CAAQ;IAE3C,UAAU,IAAI,IAAI;IAc3B,IAAI,GAAG,IAAI,MAAM,CAEhB;IAED,IAAI,GAAG,IAAI,MAAM,CAEhB;IAED,kBAAkB,IAAI,YAAY,EAAE;IAOtB,oBAAoB,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,MAAM,GAAG,IAAI;IAa/E,wBAAwB,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,GAAG,IAAI;IA0B7C,gBAAgB,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,GAAG,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAe/E,cAAc,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IA6ExE,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI;IAyDxE,KAAK,IAAI,IAAI;CAYd"}