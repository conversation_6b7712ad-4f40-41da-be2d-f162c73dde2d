#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试SearchableComboBox组件的修复
"""

import sys
import os
sys.path.append('src')

from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from views.custom_widgets import SearchableComboBox

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SearchableComboBox 测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("测试SearchableComboBox组件:")
        layout.addWidget(label)
        
        # 创建SearchableComboBox
        self.combo = SearchableComboBox()
        
        # 添加测试数据
        test_items = [
            "样车编号",
            "问题描述 QTM DTS DTM",
            "测试软件版本",
            "问题日期 QTM DTS DTM",
            "结度",
            "经度",
            "提出人",
            "严重度 QTM DTS DTM",
            "行车问题分类",
            "接线类型",
            "场景类型",
            "功能模式",
            "道路类型",
            "问题发生位置"
        ]
        
        self.combo.addItems(test_items)
        layout.addWidget(self.combo)
        
        # 添加说明
        instructions = QLabel("""
测试说明:
1. 输入无匹配项的文字，检查是否会自动清空
2. 选择一个字段后，检查下拉框是否显示完整列表
3. 搜索后选择字段，检查交互是否正常
        """)
        layout.addWidget(instructions)

def main():
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
