{"version": 3, "file": "box_zoom_tool.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/lib/models/tools/gestures/box_zoom_tool.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,WAAW,EAAE,eAAe,EAAC,MAAM,gBAAgB,CAAA;AAC3D,OAAO,EAAC,aAAa,EAAC,MAAM,kCAAkC,CAAA;AAC9D,OAAO,EAAC,cAAc,EAAC,MAAM,8BAA8B,CAAA;AAC3D,OAAO,KAAK,CAAC,iCAAuB;AACpC,OAAO,EAAC,QAAQ,EAAC,gCAAsB;AACvC,OAAO,EAAC,UAAU,EAAE,SAAS,EAAC,4BAAkB;AAIhD,qBAAa,eAAgB,SAAQ,eAAe;IACzC,KAAK,EAAE,WAAW,CAAA;IAE3B,SAAS,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,CAAA;IAE9C,aAAa,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EACxD,KAAK,EAAE,cAAc,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAmE1E,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAqBlF,UAAU,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI;IAI9B,IAAI,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI;IAMxB,QAAQ,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI;IAWrC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI;CA0B1E;AAkBD,yBAAiB,WAAW,CAAC;IAC3B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,WAAW,CAAC,KAAK,GAAG;QACtC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAClC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;QAClC,YAAY,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QACjC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;KAC9B,CAAA;CACF;AAED,MAAM,WAAW,WAAY,SAAQ,WAAW,CAAC,KAAK;CAAG;AAEzD,qBAAa,WAAY,SAAQ,WAAW;IACjC,UAAU,EAAE,WAAW,CAAC,KAAK,CAAA;IAC7B,aAAa,EAAE,eAAe,CAAA;IAE9B,OAAO,EAAE,aAAa,CAAA;gBAEnB,KAAK,CAAC,EAAE,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC;IAmBrC,SAAS,SAAa;IACtB,IAAI,SAAqB;IACzB,UAAU,QAAiB;IAC3B,aAAa,SAAK;IAE3B,IAAa,OAAO,IAAI,MAAM,CAE7B;CACF"}