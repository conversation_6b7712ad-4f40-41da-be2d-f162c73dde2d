{"version": 3, "file": "ui_events.d.ts", "sourceRoot": "", "sources": ["../../../../src/lib/core/ui_events.ts"], "names": [], "mappings": "AAEA,OAAO,EAAC,MAAM,EAAC,MAAM,aAAa,CAAA;AAElC,OAAO,EAAS,IAAI,EAAC,MAAM,OAAO,CAAA;AAMlC,OAAO,EAAC,QAAQ,EAAC,MAAM,sBAAsB,CAAA;AAC7C,OAAO,EAAC,QAAQ,EAAC,MAAM,sBAAsB,CAAA;AAE7C,OAAO,EAAC,YAAY,EAAC,MAAM,8BAA8B,CAAA;AACzD,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,yBAAyB,CAAA;AAMvD,aAAK,WAAW,GAAG;IACjB,IAAI,EAAE,MAAM,CAAA;IACZ,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;IACd,KAAK,EAAE,MAAM,CAAA;IACb,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE,UAAU,GAAG,UAAU,GAAG,YAAY,CAAA;CACjD,CAAA;AAED,oBAAY,WAAW,GAAG;IAAC,EAAE,EAAE,MAAM,CAAC;IAAC,EAAE,EAAE,MAAM,CAAA;CAAC,CAAA;AAElD,oBAAY,QAAQ,GAAG;IACrB,IAAI,EAAE,KAAK,GAAG,UAAU,GAAG,QAAQ,CAAA;IACnC,EAAE,EAAE,MAAM,CAAA;IACV,EAAE,EAAE,MAAM,CAAA;IACV,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;IACd,QAAQ,EAAE,OAAO,CAAA;IACjB,OAAO,EAAE,OAAO,CAAA;CACjB,CAAA;AAED,oBAAY,UAAU,GAAG;IACvB,IAAI,EAAE,OAAO,GAAG,YAAY,GAAG,UAAU,CAAA;IACzC,EAAE,EAAE,MAAM,CAAA;IACV,EAAE,EAAE,MAAM,CAAA;IACV,KAAK,EAAE,MAAM,CAAA;IACb,QAAQ,EAAE,OAAO,CAAA;IACjB,OAAO,EAAE,OAAO,CAAA;CACjB,CAAA;AAED,oBAAY,WAAW,GAAG;IACxB,IAAI,EAAE,QAAQ,GAAG,aAAa,GAAG,WAAW,CAAA;IAC5C,EAAE,EAAE,MAAM,CAAA;IACV,EAAE,EAAE,MAAM,CAAA;IACV,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE,OAAO,CAAA;IACjB,OAAO,EAAE,OAAO,CAAA;CACjB,CAAA;AAED,oBAAY,YAAY,GAAG,QAAQ,GAAG,UAAU,GAAG,WAAW,CAAA;AAE9D,oBAAY,QAAQ,GAAG;IACrB,IAAI,EAAE,KAAK,GAAG,WAAW,GAAG,OAAO,GAAG,SAAS,GAAG,aAAa,CAAA;IAC/D,EAAE,EAAE,MAAM,CAAA;IACV,EAAE,EAAE,MAAM,CAAA;IACV,QAAQ,EAAE,OAAO,CAAA;IACjB,OAAO,EAAE,OAAO,CAAA;CACjB,CAAA;AAED,oBAAY,SAAS,GAAG;IACtB,IAAI,EAAE,WAAW,GAAG,YAAY,GAAG,YAAY,CAAA;IAC/C,EAAE,EAAE,MAAM,CAAA;IACV,EAAE,EAAE,MAAM,CAAA;IACV,QAAQ,EAAE,OAAO,CAAA;IACjB,OAAO,EAAE,OAAO,CAAA;CACjB,CAAA;AAED,oBAAY,WAAW,GAAG;IACxB,IAAI,EAAE,OAAO,CAAA;IACb,EAAE,EAAE,MAAM,CAAA;IACV,EAAE,EAAE,MAAM,CAAA;IACV,KAAK,EAAE,MAAM,CAAA;IACb,QAAQ,EAAE,OAAO,CAAA;IACjB,OAAO,EAAE,OAAO,CAAA;CACjB,CAAA;AAED,oBAAY,OAAO,GAAG,YAAY,GAAG,QAAQ,GAAG,SAAS,GAAG,WAAW,CAAA;AAEvE,oBAAY,QAAQ,GAAG;IACrB,IAAI,EAAE,OAAO,GAAG,SAAS,CAAA;IACzB,OAAO,EAAE,IAAI,CAAA;CACd,CAAA;AAED,oBAAY,SAAS,GAAG,KAAK,GAAG,OAAO,GAAG,QAAQ,GAAG,MAAM,GAAG,KAAK,GAAG,OAAO,GAAG,SAAS,GAAG,QAAQ,CAAA;AAEpG,oBAAY,QAAQ,CAAC,CAAC,IAAI,MAAM,CAAC;IAAC,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC;IAAC,CAAC,EAAE,CAAC,CAAA;CAAC,EAAE,UAAU,CAAC,CAAA;AAEvE,qBAAa,UAAW,YAAW,mBAAmB;IAsCxC,QAAQ,CAAC,WAAW,EAAE,UAAU;IArC5C,QAAQ,CAAC,SAAS,EAAK,QAAQ,CAAC,QAAQ,CAAC,CAAgC;IACzE,QAAQ,CAAC,GAAG,EAAW,QAAQ,CAAC,QAAQ,CAAC,CAA0B;IACnE,QAAQ,CAAC,OAAO,EAAO,QAAQ,CAAC,QAAQ,CAAC,CAA8B;IAEvE,QAAQ,CAAC,WAAW,EAAG,QAAQ,CAAC,UAAU,CAAC,CAAkC;IAC7E,QAAQ,CAAC,KAAK,EAAS,QAAQ,CAAC,UAAU,CAAC,CAA4B;IACvE,QAAQ,CAAC,SAAS,EAAK,QAAQ,CAAC,UAAU,CAAC,CAAgC;IAE3E,QAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,WAAW,CAAC,CAAmC;IAC/E,QAAQ,CAAC,MAAM,EAAQ,QAAQ,CAAC,WAAW,CAAC,CAA6B;IACzE,QAAQ,CAAC,UAAU,EAAI,QAAQ,CAAC,WAAW,CAAC,CAAiC;IAE7E,QAAQ,CAAC,GAAG,EAAW,QAAQ,CAAC,QAAQ,CAAC,CAA8B;IACvE,QAAQ,CAAC,SAAS,EAAK,QAAQ,CAAC,QAAQ,CAAC,CAAoC;IAC7E,QAAQ,CAAC,KAAK,EAAS,QAAQ,CAAC,QAAQ,CAAC,CAAgC;IACzE,QAAQ,CAAC,OAAO,EAAO,QAAQ,CAAC,QAAQ,CAAC,CAAkC;IAE3E,QAAQ,CAAC,UAAU,EAAI,QAAQ,CAAC,SAAS,CAAC,CAAoC;IAC9E,QAAQ,CAAC,IAAI,EAAU,QAAQ,CAAC,SAAS,CAAC,CAA8B;IACxE,QAAQ,CAAC,SAAS,EAAK,QAAQ,CAAC,SAAS,CAAC,CAAmC;IAE7E,QAAQ,CAAC,MAAM,EAAQ,QAAQ,CAAC,WAAW,CAAC,CAA8B;IAE1E,QAAQ,CAAC,OAAO,EAAO,QAAQ,CAAC,QAAQ,CAAC,CAAkC;IAC3E,QAAQ,CAAC,KAAK,EAAS,QAAQ,CAAC,QAAQ,CAAC,CAAgC;IAEzE,OAAO,CAAC,QAAQ,CAAC,MAAM,CAGrB;IAEF,OAAO,CAAC,IAAI,CAAa;IAEzB,IAAI,QAAQ,IAAI,WAAW,CAE1B;gBAEoB,WAAW,EAAE,UAAU;IAyB5C,OAAO,IAAI,IAAI;IAOf,WAAW,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAOnC,SAAS,CAAC,mBAAmB,IAAI,IAAI;IA2BrC,aAAa,CAAC,SAAS,EAAE,QAAQ,GAAG,IAAI;IAaxC,OAAO,CAAC,cAAc;IA2EtB,SAAS,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,YAAY,GAAG,IAAI;IAW/F,UAAU,CAAC,MAAM,GAAE,MAAkB,GAAG,IAAI;IAI5C,SAAS,CAAC,eAAe,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,OAAO;IAI/E,SAAS,CAAC,gBAAgB,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,OAAO;IAIhF,SAAS,CAAC,cAAc,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,QAAQ,GAAG,IAAI;IAUjE,SAAS,CAAC,UAAU,EAAE;QAAC,EAAE,EAAE,MAAM,CAAC;QAAC,EAAE,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,QAAQ,GAAG,IAAI,CAAA;KAAC,GAAG,IAAI,CAAO;IAExF,SAAS,CAAC,SAAS,EAAE;QAAC,SAAS,EAAE,QAAQ,CAAA;KAAC,GAAG,IAAI,CAAO;IACxD,SAAS,CAAC,WAAW,EAAE;QAAC,SAAS,EAAE,QAAQ,CAAA;KAAC,GAAG,IAAI,CAAO;IAC1D,SAAS,CAAC,YAAY,EAAE;QAAC,SAAS,EAAE,QAAQ,CAAA;KAAC,GAAG,IAAI,CAAO;IAE3D,QAAQ,CAAC,CAAC,SAAS,OAAO,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,GAAG,IAAI;IA2F7E,SAAS,CAAC,CAAC,SAAS,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,KAAK,GAAG,IAAI;IA2FnG,OAAO,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,GAAE,MAAM,GAAG,IAAW,GAAG,IAAI;IAIvD,oBAAoB,CAAC,SAAS,EAAE,QAAQ,EAAE,CAAC,EAAE,OAAO,GAAG,IAAI;IAkD7D,QAAQ,CAAC,KAAK,EAAE,UAAU,GAAG,UAAU,GAAG,YAAY,GAAG,WAAW;IASpE,UAAU,CAAC,CAAC,EAAE,WAAW,GAAG,QAAQ;IAWpC,YAAY,CAAC,CAAC,EAAE,WAAW,GAAG,UAAU;IAUxC,aAAa,CAAC,CAAC,EAAE,WAAW,GAAG,WAAW;IAU1C,UAAU,CAAC,CAAC,EAAE,WAAW,GAAG,QAAQ;IASpC,WAAW,CAAC,CAAC,EAAE,UAAU,GAAG,SAAS;IASrC,aAAa,CAAC,CAAC,EAAE,UAAU,GAAG,WAAW;IAUzC,UAAU,CAAC,CAAC,EAAE,aAAa,GAAG,QAAQ;IAOtC,UAAU,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAQhC,IAAI,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAI1B,QAAQ,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAI9B,YAAY,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAIlC,MAAM,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAI5B,UAAU,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAIhC,aAAa,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAInC,OAAO,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAI7B,WAAW,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAIjC,IAAI,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAI1B,UAAU,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAIhC,MAAM,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAI5B,QAAQ,CAAC,CAAC,EAAE,WAAW,GAAG,IAAI;IAI9B,YAAY,CAAC,CAAC,EAAE,UAAU,GAAG,IAAI;IAIjC,WAAW,CAAC,CAAC,EAAE,UAAU,GAAG,IAAI;IAIhC,WAAW,CAAC,CAAC,EAAE,UAAU,GAAG,IAAI;IAIhC,YAAY,CAAC,CAAC,EAAE,UAAU,GAAG,IAAI;IAIjC,aAAa,CAAC,CAAC,EAAE,UAAU,GAAG,IAAI;IASlC,SAAS,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;IAKjC,OAAO,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI;CAI5C"}