{"version": 3, "file": "affine.d.ts", "sourceRoot": "", "sources": ["../../../../../src/lib/core/util/affine.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,SAAS,EAAC,MAAM,UAAU,CAAA;AAIlC,oBAAY,KAAK,GAAG;IAAC,CAAC,EAAE,MAAM,CAAC;IAAC,CAAC,EAAE,MAAM,CAAA;CAAC,CAAA;AAC1C,oBAAY,IAAI,GAAG;IAAC,EAAE,EAAE,KAAK,CAAC;IAAC,EAAE,EAAE,KAAK,CAAC;IAAC,EAAE,EAAE,KAAK,CAAC;IAAC,EAAE,EAAE,KAAK,CAAA;CAAC,CAAA;AAE/D,qBAAa,eAAe;IAGxB,OAAO,CAAC,CAAC;IACT,OAAO,CAAC,CAAC;IACT,OAAO,CAAC,CAAC;IACT,OAAO,CAAC,CAAC;IACT,OAAO,CAAC,CAAC;IACT,OAAO,CAAC,CAAC;gBALD,CAAC,GAAE,MAAU,EACb,CAAC,GAAE,MAAU,EACb,CAAC,GAAE,MAAU,EACb,CAAC,GAAE,MAAU,EACb,CAAC,GAAE,MAAU,EACb,CAAC,GAAE,MAAU;IAEvB,QAAQ,IAAI,MAAM;IAKlB,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,GAAG,eAAe;IAKzD,YAAY,IAAI,SAAS;IAKzB,KAAK,IAAI,eAAe;IAKxB,IAAI,WAAW,IAAI,OAAO,CAGzB;IAED,WAAW,CAAC,CAAC,EAAE,KAAK,GAAG,KAAK;IAK5B,UAAU,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;IAQ5B,KAAK,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;IAQ7C,QAAQ,CAAC,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI;IAY5D,SAAS,CAAC,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,GAAG,IAAI;IAWjF,SAAS,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,IAAI;IAIvC,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,IAAI;IAInC,IAAI,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,MAAM,GAAG,IAAI;IAIlC,MAAM,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAM3B,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;IAI/B,WAAW,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI;IAI7B,WAAW,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI;IAI7B,IAAI,IAAI,IAAI;IAIZ,MAAM,IAAI,IAAI;IAId,MAAM,IAAI,IAAI;CAGf"}