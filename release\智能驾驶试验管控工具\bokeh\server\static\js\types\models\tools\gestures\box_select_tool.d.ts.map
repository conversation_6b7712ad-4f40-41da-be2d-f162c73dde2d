{"version": 3, "file": "box_select_tool.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/lib/models/tools/gestures/box_select_tool.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,UAAU,EAAE,cAAc,EAAC,MAAM,eAAe,CAAA;AACxD,OAAO,EAAC,aAAa,EAAC,MAAM,kCAAkC,CAAA;AAC9D,OAAO,KAAK,CAAC,iCAAuB;AACpC,OAAO,EAAC,UAAU,EAAE,SAAS,EAAE,aAAa,EAAC,4BAAkB;AAC/D,OAAO,EAAC,QAAQ,EAAC,gCAAsB;AAIvC,qBAAa,iBAAkB,SAAQ,cAAc;IAC1C,KAAK,EAAE,aAAa,CAAA;IAE7B,SAAS,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,CAAA;IAE9C,SAAS,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;IAclF,UAAU,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI;IAK9B,IAAI,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI;IAYxB,QAAQ,CAAC,EAAE,EAAE,QAAQ,GAAG,IAAI;IAcrC,UAAU,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,GAAE,aAAyB,GAAG,IAAI;CAI9H;AAkBD,yBAAiB,aAAa,CAAC;IAC7B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG;QACrC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAA;QAClC,sBAAsB,EAAE,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QAC3C,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAA;QAClC,MAAM,EAAE,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;KAC9B,CAAA;CACF;AAED,MAAM,WAAW,aAAc,SAAQ,aAAa,CAAC,KAAK;CAAG;AAE7D,qBAAa,aAAc,SAAQ,UAAU;IAClC,UAAU,EAAE,aAAa,CAAC,KAAK,CAAA;IAC/B,aAAa,EAAE,iBAAiB,CAAA;IAEhC,OAAO,EAAE,aAAa,CAAA;gBAEnB,KAAK,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC;IAmBvC,SAAS,SAAe;IACxB,IAAI,SAAuB;IAC3B,UAAU,QAAiB;IAC3B,aAAa,SAAK;IAE3B,IAAa,OAAO,IAAI,MAAM,CAE7B;CACF"}