{"version": 3, "file": "cell_editors.d.ts", "sourceRoot": "", "sources": ["../../../../../../src/lib/models/widgets/tables/cell_editors.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,CAAC,iCAAuB;AAGpC,OAAO,EAAC,OAAO,EAAC,+BAAqB;AACrC,OAAO,EAAC,KAAK,EAAC,MAAM,gBAAgB,CAAA;AACpC,OAAO,EAAe,IAAI,EAAC,MAAM,eAAe,CAAA;AAIhD,8BAAsB,cAAe,SAAQ,OAAO;IACzC,KAAK,EAAE,UAAU,CAAA;IACjB,EAAE,EAAE,WAAW,CAAA;IAExB,YAAY,EAAE,GAAG,CAAA;IAEjB,IAAI,UAAU,IAAI,GAAG,CAEpB;IAED,OAAO,EAAE,gBAAgB,GAAG,iBAAiB,GAAG,mBAAmB,CAAA;IAEnE,SAAS,CAAC,IAAI,EAAE,GAAG,CAAA;IAEnB,SAAS,CAAC,QAAQ,CAAC,YAAY,IAAI,gBAAgB,GAAG,iBAAiB,GAAG,mBAAmB;gBAEjF,OAAO,EAAE,GAAG;IAQf,UAAU,IAAI,IAAI;IAMZ,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC;IAItC,WAAW,IAAI,MAAM,EAAE;IAIvB,MAAM,IAAI,IAAI;IAQvB,YAAY,IAAI,IAAI;IAEpB,iBAAiB,IAAI,IAAI;IAezB,OAAO,IAAI,IAAI;IAIf,KAAK,IAAI,IAAI;IAIb,IAAI,IAAI,IAAI;IAEZ,IAAI,IAAI,IAAI;IAEZ,QAAQ,IAAI,GAAG;IAEf,QAAQ,IAAI,GAAG;IAIf,QAAQ,CAAC,GAAG,EAAE,GAAG,GAAG,IAAI;IAIxB,cAAc,IAAI,GAAG;IAIrB,cAAc,IAAI,OAAO;IAIzB,UAAU,CAAC,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,GAAG,IAAI;IAMxC,SAAS,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;IAM3B,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;IAW9B,QAAQ,IAAI,GAAG;CAGhB;AAED,yBAAiB,UAAU,CAAC;IAC1B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,KAAK,CAAC,KAAK,CAAA;CAChC;AAED,MAAM,WAAW,UAAW,SAAQ,UAAU,CAAC,KAAK;CAAG;AAEvD,8BAAsB,UAAW,SAAQ,KAAK;IACnC,UAAU,EAAE,UAAU,CAAC,KAAK,CAAA;IAC5B,aAAa,EAAE,cAAc,CAAA;CACvC;AAED,qBAAa,gBAAiB,SAAQ,cAAc;IACzC,KAAK,EAAE,YAAY,CAAA;IAEnB,OAAO,EAAE,gBAAgB,CAAA;IAElC,IAAa,UAAU,IAAI,MAAM,CAEhC;IAED,SAAS,CAAC,YAAY,IAAI,gBAAgB;IAIjC,YAAY,IAAI,IAAI;IAUpB,SAAS,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;CAKrC;AAED,yBAAiB,YAAY,CAAC;IAC5B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG;QACrC,WAAW,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;KAClC,CAAA;CACF;AAED,MAAM,WAAW,YAAa,SAAQ,YAAY,CAAC,KAAK;CAAG;AAE3D,qBAAa,YAAa,SAAQ,UAAU;IACjC,UAAU,EAAE,YAAY,CAAC,KAAK,CAAA;CAQxC;AAED,qBAAa,cAAe,SAAQ,cAAc;IACvC,KAAK,EAAE,UAAU,CAAA;IAEjB,OAAO,EAAE,mBAAmB,CAAA;IAErC,SAAS,CAAC,YAAY,IAAI,mBAAmB;IAIpC,YAAY,IAAI,IAAI;CAI9B;AAED,yBAAiB,UAAU,CAAC;IAC1B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,CAAA;CACrC;AAED,MAAM,WAAW,UAAW,SAAQ,UAAU,CAAC,KAAK;CAAG;AAEvD,qBAAa,UAAW,SAAQ,UAAU;IAC/B,UAAU,EAAE,UAAU,CAAC,KAAK,CAAA;CAKtC;AAED,qBAAa,gBAAiB,SAAQ,cAAc;IACzC,KAAK,EAAE,YAAY,CAAA;IAEnB,OAAO,EAAE,iBAAiB,CAAA;IAEnC,SAAS,CAAC,YAAY,IAAI,iBAAiB;IAIlC,YAAY,IAAI,IAAI;CAM9B;AAED,yBAAiB,YAAY,CAAC;IAC5B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG;QACrC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAA;KAC9B,CAAA;CACF;AAED,MAAM,WAAW,YAAa,SAAQ,YAAY,CAAC,KAAK;CAAG;AAE3D,qBAAa,YAAa,SAAQ,UAAU;IACjC,UAAU,EAAE,YAAY,CAAC,KAAK,CAAA;CAQxC;AAED,qBAAa,iBAAkB,SAAQ,cAAc;IAC1C,KAAK,EAAE,aAAa,CAAA;IAEpB,OAAO,EAAE,gBAAgB,CAAA;IAElC,SAAS,CAAC,YAAY,IAAI,gBAAgB;CAG3C;AAED,yBAAiB,aAAa,CAAC;IAC7B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,CAAA;CACrC;AAED,MAAM,WAAW,aAAc,SAAQ,aAAa,CAAC,KAAK;CAAG;AAE7D,qBAAa,aAAc,SAAQ,UAAU;IAClC,UAAU,EAAE,aAAa,CAAC,KAAK,CAAA;CAKzC;AAED,qBAAa,kBAAmB,SAAQ,cAAc;IAC3C,KAAK,EAAE,cAAc,CAAA;IAErB,OAAO,EAAE,gBAAgB,CAAA;IAElC,SAAS,CAAC,YAAY,IAAI,gBAAgB;IAIjC,YAAY,IAAI,IAAI;IAIpB,SAAS,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;IAK3B,cAAc,IAAI,GAAG;CAG/B;AAED,yBAAiB,cAAc,CAAC;IAC9B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,CAAA;CACrC;AAED,MAAM,WAAW,cAAe,SAAQ,cAAc,CAAC,KAAK;CAAG;AAE/D,qBAAa,cAAe,SAAQ,UAAU;IACnC,UAAU,EAAE,cAAc,CAAC,KAAK,CAAA;CAK1C;AAED,qBAAa,aAAc,SAAQ,cAAc;IACtC,KAAK,EAAE,SAAS,CAAA;IAEhB,OAAO,EAAE,gBAAgB,CAAA;IAElC,SAAS,CAAC,YAAY,IAAI,gBAAgB;IAIjC,YAAY,IAAI,IAAI;IAMpB,MAAM,IAAI,IAAI;IAKd,cAAc,IAAI,GAAG;IAIrB,SAAS,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;IAM3B,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;CAMxC;AAED,yBAAiB,SAAS,CAAC;IACzB,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG;QACrC,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;KACzB,CAAA;CACF;AAED,MAAM,WAAW,SAAU,SAAQ,SAAS,CAAC,KAAK;CAAG;AAErD,qBAAa,SAAU,SAAQ,UAAU;IAC9B,UAAU,EAAE,SAAS,CAAC,KAAK,CAAA;CAQrC;AAED,qBAAa,gBAAiB,SAAQ,cAAc;IACzC,KAAK,EAAE,YAAY,CAAA;IAEnB,OAAO,EAAE,gBAAgB,CAAA;IAElC,SAAS,CAAC,YAAY,IAAI,gBAAgB;IAIjC,YAAY,IAAI,IAAI;IAMpB,MAAM,IAAI,IAAI;IAKd,cAAc,IAAI,GAAG;IAIrB,SAAS,CAAC,IAAI,EAAE,IAAI,GAAG,IAAI;IAM3B,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,GAAG;CAMxC;AAED,yBAAiB,YAAY,CAAC;IAC5B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG;QACrC,IAAI,EAAE,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;KACzB,CAAA;CACF;AAED,MAAM,WAAW,YAAa,SAAQ,YAAY,CAAC,KAAK;CAAG;AAE3D,qBAAa,YAAa,SAAQ,UAAU;IACjC,UAAU,EAAE,YAAY,CAAC,KAAK,CAAA;CAQxC;AAED,qBAAa,cAAe,SAAQ,cAAc;IACvC,KAAK,EAAE,UAAU,CAAA;IAEjB,OAAO,EAAE,gBAAgB,CAAA;IAElC,SAAS,CAAC,YAAY,IAAI,gBAAgB;CAG3C;AAED,yBAAiB,UAAU,CAAC;IAC1B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,CAAA;CACrC;AAED,MAAM,WAAW,UAAW,SAAQ,UAAU,CAAC,KAAK;CAAG;AAEvD,qBAAa,UAAW,SAAQ,UAAU;IAC/B,UAAU,EAAE,UAAU,CAAC,KAAK,CAAA;CAKtC;AAED,qBAAa,cAAe,SAAQ,cAAc;IACvC,KAAK,EAAE,UAAU,CAAA;IAEjB,OAAO,EAAE,gBAAgB,CAAA;IAElC,SAAS,CAAC,YAAY,IAAI,gBAAgB;IAI1C,IAAa,UAAU,IAAI,IAAI,CAE9B;IAEQ,YAAY,IAAI,IAAI;IAepB,OAAO,IAAI,IAAI;IAOf,IAAI,IAAI,IAAI;IAMZ,IAAI,IAAI,IAAI;IAMZ,QAAQ,IAAiB,GAAG;IAM5B,QAAQ,IAAI,GAAG;IAIf,QAAQ,CAAC,IAAI,EAAE,GAAG,GAAG,IAAI;CAGnC;AAED,yBAAiB,UAAU,CAAC;IAC1B,KAAY,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;IAEpC,KAAY,KAAK,GAAG,UAAU,CAAC,KAAK,CAAA;CACrC;AAED,MAAM,WAAW,UAAW,SAAQ,UAAU,CAAC,KAAK;CAAG;AAEvD,qBAAa,UAAW,SAAQ,UAAU;IAC/B,UAAU,EAAE,UAAU,CAAC,KAAK,CAAA;CAKtC"}